﻿module IDELDEditor
plugin IDELDEditor
typeinfo plugins.qmltypes
LD_AND 1.0 qrc:/qml/LDEdit/LD_AND.qml
LD_COIL 1.0 qrc:/qml/LDEdit/LD_COIL.qml
LD_CONTACT 1.0 qrc:/qml/LDEdit/LD_CONTACT.qml
LD_END 1.0 qrc:/qml/LDEdit/LD_END.qml
LD_FUNCTION 1.0 qrc:/qml/LDEdit/LD_FUNCTION.qml
LD_FUNCTIONBLOCK 1.0 qrc:/qml/LDEdit/LD_FUNCTIONBLOCK.qml
LD_JMP 1.0 qrc:/qml/LDEdit/LD_JMP.qml
LD_OR 1.0 qrc:/qml/LDEdit/LD_OR.qml
LD_RETURN 1.0 qrc:/qml/LDEdit/LD_RETURN.qml
LD_Set0 1.0 qrc:/qml/LDEdit/LD_Set0.qml
LD_Set1 1.0 qrc:/qml/LDEdit/LD_Set1.qml
LDBlock 1.0 qrc:/qml/LDEdit/LDBlock.qml
LDEditor 1.0 qrc:/qml/LDEdit/LDEditor.qml
LDFunctionBlockInsertDialog 1.0 qrc:/qml/LDEdit/LDFunctionBlockInsertDialog.qml
LDFunctionInsertDialog 1.0 qrc:/qml/LDEdit/LDFunctionInsertDialog.qml
LDNetwork 1.0 qrc:/qml/LDEdit/LDNetwork.qml
LDVariableInsertDialog 1.0 qrc:/qml/LDEdit/LDVariableInsertDialog.qml
MultiLDEditor 1.0 qrc:/qml/LDEdit/MultiLDEditor.qml

FunctionBlockEleSelect 1.0 qrc:/qml/LDEditor/FunctionBlockEleSelect.qml
FunctionBlockSelect 1.0 qrc:/qml/LDEditor/FunctionBlockSelect.qml
LDConnections 1.0 qrc:/qml/LDEditor/LDConnections.qml
LD_COIL 1.0 qrc:/qml/LDEditor/LD_COIL.qml
LD_CONTACT 1.0 qrc:/qml/LDEditor/LD_CONTACT.qml
LD_FUN 1.0 qrc:/qml/LDEditor/LD_FUN.qml
LD_OR 1.0 qrc:/qml/LDEditor/LD_OR.qml
LDBlock 1.0 qrc:/qml/LDEditor/LDBlock.qml
LDEditor 1.0 qrc:/qml/LDEditor/LDEditor.qml
LDEditorView 1.0 qrc:/qml/LDEditor/LDEditorView.qml
LDMultiShortcuts 1.0 qrc:/qml/LDEditor/LDMultiShortcuts.qml
LDNetwork 1.0 qrc:/qml/LDEditor/LDNetwork.qml
LDPositionBlocks 1.0 qrc:/qml/LDEditor/LDPositionBlock.qml
MultiLDEditor 1.0 qrc:/qml/LDEditor/MultiLDEditor.qml
VariableInput 1.0 qrc:/qml/LDEditor/VariableInput.qml
VariableSelect 1.0 qrc:/qml/LDEditor/VariableSelect.qml
