import QtQuick 2.15
import QtQuick.Controls 2.15

Rectangle {
    id: control
    // 主控件
    property var mainControl
    // 当前所在的网络组件
    property var netWorkControl
    // 配置
    property var config: LDEditorConfiger
    // 块数据
    property var blockData
    // 记录当前选中的网络中对应的start块元件序号
    property int startBlockNumber
    // 当前所在的网络号
    property int netWorkNumber

    width: blockData.Width * config.cellWidth
    height: blockData.Height * config.cellHeight
    color: "transparent"
    // start块元件不可见
    visible: startBlockNumber !== blockData.Number

    Rectangle {
        width: parent.width - config.defaultLineWidth
        height: parent.height
        color: isShear ? "transparent" : (parent.parent.focus ? "#6495ed" : "transparent")
    }

    Text {
        visible: isTest && blockData.Number !== startBlockNumber
        text: "(" + blockData.XPos + "," + blockData.YPos + ")"
        font.pixelSize: config.fontPixelSize
        anchors.horizontalCenter: parent.horizontalCenter
    }

    Row {
        anchors.fill: parent
        Rectangle {
            anchors.top: parent.top
            anchors.topMargin: parent.height / 2 - config.defaultLineWidth
            height: config.defaultLineWidth
            width: blockData.Width / 5.5 * 2 * config.cellWidth
            color: config.defaultConnectionLineColor
        }

        Row {
            anchors.top: parent.top
            anchors.topMargin: config.cellHeight
            anchors.bottom: parent.bottom
            anchors.bottomMargin: config.cellHeight
            height: parent.height
            width: blockData.Width / 5.5 * 1.5 * config.cellWidth
            spacing: 5
            Rectangle {
                height: parent.height
                width: config.defaultLineWidth
                color: "#000000"
            }

            // 置反线条
            Rectangle {
                width: parent.width - config.defaultLineWidth * 2 - parent.spacing * 2
                height: parent.height
                color: "transparent"

                // Text {
                //     anchors.fill: parent
                //     horizontalAlignment: Text.AlignHCenter
                //     verticalAlignment: Text.AlignVCenter
                //     text: "OK"
                //     font.pixelSize: 16
                //     color: "#000000"
                // }

                Text {
                    anchors.fill: parent
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    text: getContactText()
                    font.pixelSize: 22
                    color: "#000000"
                }
            }

            Rectangle {
                height: parent.height
                width: config.defaultLineWidth
                color: "#000000"
            }
        }

        Rectangle {
            anchors.top: parent.top
            anchors.topMargin: parent.height / 2 - config.defaultLineWidth
            height: config.defaultLineWidth
            width: blockData.Width / 5.5 * 2 * config.cellWidth
            color: config.defaultConnectionLineColor
        }
    }

    MouseArea {
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        // 长按时间为50毫秒
        pressAndHoldInterval: 100
        property bool isLongPress: false
        // 开始长按时的坐标
        property int startLongPressX: 0
        property int startLongPressY: 0
        onClicked: {
            activeFocusItem = "BLOCK"
            parent.forceActiveFocus()
            mainControl.startBlockNumber = control.startBlockNumber
            mainControl.netWorkNumber = control.netWorkNumber
            if (mouse.button === Qt.RightButton)
            {
                const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                mainControl.showMenu(positionInRoot.x, positionInRoot.y, "BLOCK", getBlockType(blockData), blockData,
                                     includeOrMaxXPosList[blockData.YPos] === blockData.XPos)
            }
            else
            {
                mainControl.currentSelectBlockType = getBlockType(blockData)
                mainControl.currentSelectBolckData = blockData
            }
        }
        onPressAndHold:  {
            activeFocusItem = "BLOCK"
            parent.forceActiveFocus()
            mainControl.startBlockNumber = control.startBlockNumber
            mainControl.netWorkNumber = control.netWorkNumber
            const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
            if (mouse.button === Qt.RightButton)
            {
                mainControl.showMenu(positionInRoot.x, positionInRoot.y, "BLOCK", getBlockType(blockData), blockData,
                                     includeOrMaxXPosList[blockData.YPos] === blockData.XPos)
            }
            else if(mouse.button === Qt.LeftButton)
            {
                mainControl.currentSelectBlockType = getBlockType(blockData)
                mainControl.currentSelectBolckData = blockData
                // 开启了长按
                isLongPress = true
                // 当前长按的块元件id
                mainControl.currentSelectBolckData = blockData
                startLongPressX = positionInRoot.x
                startLongPressY = positionInRoot.y
            }
        }
        onReleased: {
            // 关闭了长按
            isLongPress = false
            mainControl.isShowPositionBlocks = false
            startLongPressX = 0
            startLongPressY = 0

            if(Object.keys(netWorkControl.currentSelectPositionBlock).length !== 0)
            {
                // 先执行剪切再执行粘贴将触点移动到指定位置
                const content = ldManage.cutComponent(fileKey, blockData.Number)
                const currentSelectPositionBlockNumber = netWorkControl.currentSelectPositionBlock.number
                let component

                for(let nIndex = 0; nIndex < netWorkList.length; nIndex++)
                {
                    const components = netWorkList[nIndex].components
                    component = components.find(com => com.Number === currentSelectPositionBlockNumber)

                    if(component)
                    {
                        break
                    }
                }

                mainControl.currentSelectBlockType = getBlockType(component)
                mainControl.currentSelectBolckData = component
                mainControl.copyOrCutType = getBlockType(blockData)
                mainControl.paste(parseInt(netWorkControl.currentSelectPositionBlock.position), content)
                
                netWorkControl.currentSelectPositionBlock = {}
                ldManage.fixLDFileConnections(fileKey)
            }
        }
        onPositionChanged: {
            if(isLongPress && !isFlowEndBlock(getBlockType(blockData)))
            {
                if(startLongPressX !== 0 && startLongPressY !== 0)
                {
                    // 计算与开始长按时的坐标的差值
                    const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                    const deltaX = Math.abs(positionInRoot.x - startLongPressX)
                    const deltaY = Math.abs(positionInRoot.y - startLongPressY)

                    // 只有在长按的情况下并且大幅度移动时才会显示坐标块
                    if(deltaX >= 20 || deltaY >= 20)
                    {
                        mainControl.isShowPositionBlocks = true
                        const positionInNetWork = mapToItem(netWorkControl, mouse.x, mouse.y)
                        if(positionInNetWork.x < 0 || positionInNetWork.y < 0)
                        {
                            mainControl.isShowPositionBlocks = false
                        }
                        else
                        {
                            // 触发信号,重新计算坐标块的位置
                            calculatePositionBlockPoints(positionInNetWork.x, positionInNetWork.y)
                        }
                    }
                }
            }
        }
    }

    function getContactText()
    {
        if(blockData.Negated)
        {
            // 置反
            return "/"
        }

        return ""
    }
}