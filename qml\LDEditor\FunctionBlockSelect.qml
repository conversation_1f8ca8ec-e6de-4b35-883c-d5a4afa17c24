﻿import QtGraphicalEffects 1.15
import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/qml/control/common"
import "qrc:/qml/control/table"

Popup {
    id: control
    property string deviceName: ""
    //文件名
    property string owned: ""
    //文件分类
    property string type: ""
    //检查是否已存在同名变量或功能块
    property var checkExist: null

    signal quoteFunctioned(var quoteArr)

    function init() {
        listModel.clear()
        const datas = serviceInterface.getAllFunctionAndBlock(
                        control.deviceName)
        //const datas = fbdManage.getAllFunctionAndBlock()
        const firmfunctionblock = datas["firmfunctionblock"]
        const firmfunction = datas["firmfunction"]
        const userfbs = datas["pptall"]
        const search = search_input.text.trim().toUpperCase()
        //添加用户定义的功能与功能块
        for (var i = 0; i < userfbs.length; i++) {
            if (
                search.length > 0 &&
                userfbs[i].name.toUpperCase().indexOf(search) < 0 ||
                control.type==="FUNCTION" &&
                userfbs[i].type!=="FUNCTION" ||
                control.owned.split(".")[0] === userfbs[i].name
            ){
                continue
            }

            listModel.append({
                                 "isChecked": false,
                                 "isSelected": false,
                                 "isAlternate": false,
                                 "name": userfbs[i].name,
                                 "type": userfbs[i].type
                             })
        }
        //添加功能块
        for (var i = 0; i < firmfunctionblock.length; i++) {
            if (
                search.length > 0 &&
                firmfunctionblock[i].name.toUpperCase().indexOf(search) < 0||
                control.type==="FUNCTION" &&
                firmfunctionblock[i].type!=="FUNCTION"
            ){
                continue
            }

            listModel.append({
                                 "isChecked": false,
                                 "isSelected": false,
                                 "isAlternate": false,
                                 "name": firmfunctionblock[i].name,
                                 "type": "FUNCTIONBLOCK"
                             })
        }
        //添加功能
        for (var i = 0; i < firmfunction.length; i++) {
            if (search.length > 0 && firmfunction[i].name.toUpperCase().indexOf(
                        search) < 0)
                continue

            listModel.append({
                                 "isChecked": false,
                                 "isSelected": false,
                                 "isAlternate": false,
                                 "name": firmfunction[i].name,
                                 "type": "FUNCTION"
                             })
        }
    }

    width: 400
    height: 450
    modal: true
    focus: true
    padding: 0
    closePolicy: Popup.NoAutoClose
    onOpened: {
        init()
    }

    Rectangle {
        id: popupHeader
        width: parent.width - 20
        height: 32
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.topMargin: 5
        anchors.leftMargin: 10
        color: "#f0f0f0"

        Row {
            anchors.fill: parent
            spacing: 5 // 设置子元素之间的间距

            Text {
                id: search_label

                text: "名称："
                anchors.verticalCenter: parent.verticalCenter
                font.pixelSize: 15
            }

            QkTextField {
                id: search_input

                anchors.verticalCenter: parent.verticalCenter
                width: 220
                height: 32
                selectByMouse: true
                text: ""
            }

            QkButton {
                anchors.verticalCenter: parent.verticalCenter
                text: "查找"
                width: 72
                height: 32
                onClicked: {
                    init()
                }
            }
        }
    }

    Rectangle {
        id: table
        width: parent.width - 20
        anchors.left: parent.left
        anchors.top: popupHeader.bottom
        anchors.topMargin: 5
        anchors.leftMargin: 10
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        color: "#F0F0F0"

        QkTableGird {
            id: tableGrid
            anchors.top: parent.top
            needsort: false
            showCheckColumn: true
            width: parent.width
            height: parent.height - 40
            tableData: tableData

            tableview.delegate: QkTableRow {
                id: tableRow
                parentControl: tableGrid.tableview
                width: tableGrid.width - 2
                rowObj: model
                widthList: tableGrid.widthList
                xList: tableGrid.xList
                onCheckedChanged: {
                    tableGrid.tableData.check(index, checked)
                }

                QkLabel {
                    text: tableRow.rowObj ? (String(
                                                 tableRow.rowObj["name"])) : ""
                    x: tableGrid.xList[1]
                    width: tableGrid.widthList[1]
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                QkLabel {
                    text: tableRow.rowObj ? (String(
                                                 tableRow.rowObj["type"])) : ""
                    x: tableGrid.xList[2]
                    width: tableGrid.widthList[2]
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }

        QkButton {
            anchors.top: tableGrid.bottom
            anchors.topMargin: 5
            anchors.right: parent.horizontalCenter
            anchors.rightMargin: 70
            width: 70
            height: 30
            text: "取消"
            onClicked: {
                control.close()
            }
        }

        QkButton {
            anchors.top: tableGrid.bottom
            anchors.topMargin: 5
            anchors.left: parent.horizontalCenter
            anchors.leftMargin: 70
            width: 70
            height: 30
            text: "引入"
            onClicked: {
                let flag = true
                let quetoArr = []
                for (var i = 0; i < listModel.count; i++) {
                    if (listModel.get(i).isChecked) {
                        const name = listModel.get(i).name
                        const type = listModel.get(i).type
                        let referenceName = name
                        let referenceNameSuffix = 1
                        do {
                            referenceName = name + "_" + referenceNameSuffix
                            referenceNameSuffix++
                        } while (checkExist(null, referenceName))
                        if (listModel.get(i).type === "FUNCTION") {
                            let fun = JSON.parse(JSON.stringify(
                                                     listModel.get(i)))
                            quetoArr.push(fun)
                        } else {
                            const result = VariableManage.addFunctionBlock(
                                             control.deviceName, "Local",
                                             control.owned, control.type,
                                             referenceName, name, type)
                            flag = flag && result
                        }
                    }
                    if (!flag)
                        break
                }
                if (flag)
                    control.quoteFunctioned(quetoArr)
                else
                    console.log("引入失败")
                control.close()
            }
        }
    }

    QkTableData {
        id: tableData

        headerRoles: ["名称", "类型"]
        rowDatas: listModel
    }

    ListModel {
        id: listModel
    }

    background: Rectangle {
        color: "#f0f0f0"
        radius: 8
    }
}
