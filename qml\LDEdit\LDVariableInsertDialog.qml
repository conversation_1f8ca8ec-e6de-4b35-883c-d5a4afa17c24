﻿import QtQuick 2.15
import FluentUI 1.0
import QtQuick.Controls 1.4
import "qrc:/qml/control/common"

//变量选择
Rectangle {
    id: control
    width: 920
    height: 450
    radius: 5

    //当前LD文件Key
    property string currentLDFile: ""
    property string currentLevelInfo: ""
    property string deviceName: ""
    property int pinid: 0

    property var owned: ["Global.POE", "IOM.POE"]
    property var type: ["Global", "IO", "M"]
    property bool isProgram: true // 默认LD都是程序Program
    property var dataList: []
    property string dataType: ""
    property var blockData

    //标题行
    QkButtonRow {
        id: title
        Text {
            anchors.left: parent.left
            anchors.leftMargin: 20
            anchors.verticalCenter: parent.verticalCenter

            text: qsTr("Variable Select") + (trans ? trans.transString : "")
        }
    }

    JTableView {
        id: table_view
        anchors {
            top: title.bottom
        }
        width: 920
        height: 400
        checkbox: true
        dataSource: initData()
        columnIsVisible: false
        menuIsVisible: false
        columnDataSource: [{
                "title": '作用域',
                "dataIndex": 'Scope',
                "width": 90,
                "hide": true,
                "readOnly": true,
                "minimumWidth": 90,
                "maximumWidth": 90
            }, {
                "title": '名称',
                "dataIndex": 'Name',
                "width": 220,
                "hide": true,
                "readOnly": true,
                "minimumWidth": 220,
                "maximumWidth": 250
            }, {
                "title": '数据类型',
                "dataIndex": 'DataType',
                "width": 100,
                "hide": true,
                "readOnly": true,
                "minimumWidth": 100,
                "maximumWidth": 100
            }, {
                "title": '数组长度',
                "dataIndex": 'ArrayLength',
                "width": 80,
                "hide": true,
                "readOnly": true,
                "minimumWidth": 80,
                "maximumWidth": 80
            }, {
                "title": '地址',
                "dataIndex": 'Address',
                "width": 100,
                "hide": true,
                "readOnly": true,
                "minimumWidth": 100,
                "maximumWidth": 100
            }, {
                "title": '初始值',
                "dataIndex": 'InitValue',
                "width": 100,
                "hide": true,
                "readOnly": true,
                "minimumWidth": 100,
                "maximumWidth": 100
            }, {
                "title": '描述',
                "dataIndex": 'Description',
                "width": 100,
                "hide": true,
                "readOnly": true,
                "minimumWidth": 100,
                "maximumWidth": 100
            }]
        onCheckBoxChanged: dataSource => {
                               dataList = dataSource
                           }
    }

    QkButton {
        text: qsTr("Select") + (trans ? trans.transString : "")
        width: 120
        anchors {
            right: parent.horizontalCenter
            rightMargin: 120
            bottom: parent.bottom
            bottomMargin: 25
        }
        onClicked: {
            console.log(control.currentLDFile, control.currentLevelInfo)
            commit()
        }
    }

    QkButton {
        text: qsTr("清除") + (trans ? trans.transString : "")
        width: 120
        anchors {
            horizontalCenter: parent.horizontalCenter
            bottom: parent.bottom
            bottomMargin: 25
        }
        onClicked: {
            console.log(control.currentLDFile, control.currentLevelInfo)
            clear()
        }
    }

    QkButton {
        text: qsTr("Cancel") + (trans ? trans.transString : "")
        width: 120
        anchors {
            left: parent.horizontalCenter
            leftMargin: 120
            bottom: parent.bottom
            bottomMargin: 25
        }
        onClicked: {

            popupDialog.close()
        }
    }

    FluInfoBar {
        id: infoBar
        root: table_view
    }

    function init(filekey, levelinfo, deviceName, pinid) {
        console.log("init variable dialog", filekey, levelinfo,
                    deviceName, pinid)
        currentLDFile = filekey
        currentLevelInfo = levelinfo
        control.deviceName = deviceName
        control.pinid = pinid

        //获取引脚信息
        var connectionList = ldManage.getConnection(control.currentLDFile,
                                                    control.currentLevelInfo,
                                                    control.pinid)
        if (connectionList.length > 0) {
            control.dataType = connectionList[0].DataType
        }
    }

    function commit() {
        var errorType = []
        var selectDataTypeList = []
        var selectIndexList = []
        var selectNameList = []
        var selectScopeList = []
        for (var i = 0; i < dataList.rowCount; i++) {
            var row = dataList.getRow(i)
            if (row.checkbox.options.checked) {
                selectDataTypeList.push(row.DataType)
                selectIndexList.push(i)
                selectNameList.push(row.Name)
                selectScopeList.push(row.Scope)
            }
        }
        console.log("errorType:", control.dataType, selectDataTypeList)
        errorType = ldManage.checkDataType(control.dataType, selectDataTypeList)
        console.log("errorType:", JSON.stringify(errorType))
        var errorTypeSet = new Set(errorType)
        var uniqueErrorType = Array.from(errorTypeSet)
        if (uniqueErrorType.length > 0) {
            infoBar.showError(
                        "包含了与[" + dataType + "]不兼容的类型[" + uniqueErrorType + "]",
                        5000)
            return
        }

        for (var j = 0; j < selectNameList.length; j++) {
            var sourceType = selectDataTypeList[j]
            var varName = selectNameList[j]
            var scope = selectScopeList[j]

            console.log("addVariableConnection", sourceType, varName, scope)

            var flag = ldManage.addConnectorVariable(control.currentLDFile,
                                                     control.currentLevelInfo,
                                                     control.pinid,
                                                     scope, varName)
            if (!flag) {
                infoBar.showError("引用变量失败", 5000)
                console.log("引用变量失败")
                return
            }
            popupDialog.close()
        }
    }

    function initData() {
        var dataSource = []
        //console.log("initData:", control.currentLDFile, control.deviceName,control.owned, control.type)
        if (!control.isProgram && control.currentLDFile !== "") {
            var inputData = ldManage.getInputVariables(control.currentLDFile)
            for (var k = 0; k < inputData.length; k++) {
                dataSource.push({
                                    "Scope": "Local",
                                    "Name": inputData[k].Name,
                                    "DataType": inputData[k].DataType,
                                    "ArrayLength": "",
                                    "Address": "",
                                    "InitValue": inputData[k].InitialValue,
                                    "Description": inputData[k].Comment
                                })
            }

            var outputData = ldManage.getOutputVariables(control.currentLDFile)
            for (var z = 0; z < outputData.length; z++) {
                dataSource.push({
                                    "Scope": "Local",
                                    "Name": outputData[z].Name,
                                    "DataType": outputData[z].DataType,
                                    "ArrayLength": "",
                                    "Address": "",
                                    "InitValue": outputData[z].InitialValue,
                                    "Description": outputData[z].Comment
                                })
            }
        }

        var jsonary = VariableManage.getVariableList(control.deviceName,
                                                     control.owned,
                                                     control.type)
        for (var i = 0; i < jsonary.length; i++) {
            let row = jsonary[i]
            dataSource.push({
                                "Scope": row.scope,
                                "Name": row.name,
                                "DataType": row.dataType,
                                "ArrayLength": row.arrayLength,
                                "Address": row.address,
                                "InitValue": row.initialValue,
                                "Description": row.description
                            })
        }

        const deviceIOList = DeviceAndNetworkManage.getAllShowVariablesFromDevice(
                               control.deviceName)
        for (var j = 0; j < deviceIOList.length; j++) {
            let row = deviceIOList[j]
            dataSource.push({
                                "Scope": "IO",
                                "Name": row.name,
                                "DataType": row.type,
                                "Address": row.address,
                                "ArrayLength": 1,
                                "InitValue": " ",
                                "Description": row.description
                            })
        }
        return dataSource
    }

    function clear() {
        var flag = ldManage.clearConnectorVariable(control.currentLDFile,
                                                   control.currentLevelInfo,
                                                   control.pinid)
        if (!flag) {
            infoBar.showError("清除变量引用失败", 5000)
            console.log("清除变量引用失败")
            return
        }
        popupDialog.close()
    }
}
