﻿import QtQuick 2.15
import QtQuick.Controls 2.15

//LD FUNCTION块
Item {
    id: control

    //主控件
    property var mainControl
    //配置
    property var config: LDEditorConfig
    //绑定的数据
    property var bindData

    //线条起点的偏移量
    property int lineOffset: config.defaultLineHeightOffset + config.defaultConnectorHeight
                             + config.defaultConnectorMargin

    property int enenolineWidth: config.defaultENENOWidth * 4

    width: config.defaultBlockWidth + enenolineWidth * 2
    height: config.defaultConnectorHeight + config.defaultConnectorMargin + c_bl.height

    //输入EN 输出ENO 连接线
    Repeater {
        model: bindData.Connectors
        Rectangle {
            id: c_en
            visible: (model.Type === "EN" || model.Type === "ENO")
            anchors {
                left: parent.left
                leftMargin: model.Type === "EN" ? 0 : (control.enenolineWidth
                                                       + config.defaultBlockWidth)
                top: parent.top
                topMargin: control.lineOffset
            }
            width: control.enenolineWidth
            height: config.defaultLineWidth
            color: mainControl.isOnline ? (bindData.Value ? config.defaultLineTrueColor : config.defaultLineFalseColor) : config.defaultLineColor
            //引脚的标签名称
            Text {
                anchors {
                    horizontalCenter: parent.horizontalCenter
                    top: parent.top
                    topMargin: -config.fontPixelSize
                }
                visible: config.isShowLabelName
                font.pixelSize: config.fontPixelSize
                text: model.LabelName
            }
        }
    }

    //外边框
    Rectangle {
        id: c_bl
        anchors {
            left: parent.left
            leftMargin: control.enenolineWidth
            top: parent.top
            topMargin: config.defaultConnectorHeight + config.defaultConnectorMargin
        }
        width: config.defaultBlockWidth
        height: config.defaultLineHeightOffset + (getMaxConnectorNumber() + 0.5)
                * (config.defaultConnectorHeight + config.defaultConnectorMargin)
        border {
            width: 2
            color: "gray"
        }
        //实例名称
        Text {
            anchors {
                horizontalCenter: parent.horizontalCenter
                top: parent.top
                topMargin: 5
            }
            font.pixelSize: config.fontPixelSize
            height: config.fontPixelSize + 2
            width: config.defaultBlockWidth
            horizontalAlignment: Text.AlignHCenter
            text: bindData.InstanceName
            color: "black"
        }
        //EN ENO IN OUT引脚名称显示
        Repeater {
            model: bindData.Connectors
            Text {
                visible: showConnector(model.Type)
                x: model.XOffset * (config.defaultBlockWidth / 3)
                y: config.defaultLineHeightOffset + model.YOffset
                   * (config.defaultConnectorHeight + config.defaultConnectorMargin)
                font.pixelSize: config.fontPixelSize
                height: config.defaultConnectorHeight
                width: config.defaultBlockWidth / 3
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                text: model.Name
                color: "black"
            }
        }
    }
    //块外面输入变量引脚
    Repeater {
        model: bindData.Connectors
        Rectangle {
            id: c_inrect
            visible: model.Type === "IN"
            width: (config.defaultENENOWidth - config.defaultLineWidth) * 2
            height: config.defaultConnectorHeight
            anchors {
                right: c_bl.left
                rightMargin: config.defaultENENOWidth / 2
            }
            y: config.defaultLineHeightOffset + (model.YOffset + 1)
               * (config.defaultConnectorHeight + config.defaultConnectorMargin)
            border {
                width: 1
                color: config.defaultVaiableBorderColor
            }
            color: config.defaultVaiableBackgroundColor
            MouseArea {
                anchors.fill: parent
                acceptedButtons: Qt.LeftButton | Qt.RightButton
                hoverEnabled: true
                onClicked: {
                    mainControl.selectID = bindData.LevelInfo
                    if (mouse.button === Qt.RightButton) {
                        var positionInRoot = mapToItem(mainControl,
                                                       mouse.x, mouse.y)
                        mainControl.showMenu(positionInRoot.x,
                                             positionInRoot.y, model.Type,
                                             model.PinId)
                    }
                }
                onEntered: {
                    itoolTip_DataType.visible = true
                }
                onExited: {
                    itoolTip_DataType.visible = false
                }
            }
            ToolTip {
                id: itoolTip_DataType
                parent: c_inrect
                y: -35
                delay: 500
                text: model.DataType
                timeout: 3000
            }
            //变量名称
            Text {
                id: c_intext
                anchors.centerIn: parent.Center
                font.pixelSize: config.fontPixelSize
                height: parent.height
                width: parent.width
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                text: model.LabelName
                color: config.defaultVaiableNameColor
            }
            //变量值
            Text {
                id: c_invalue
                anchors {
                    right: c_inrect.left
                    rightMargin: 0
                    top: c_inrect.top
                    topMargin: 0
                }
                font.pixelSize: config.fontPixelSize
                height: parent.height
                width: parent.width
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                text: model.Value
                color: config.defaultVaiableValueColor
            }
            //尾线
            Rectangle {
                anchors {
                    left: c_intext.right
                    leftMargin: 0
                    top: c_intext.top
                    topMargin: c_intext.height / 2
                }
                width: config.defaultENENOWidth / 2
                height: config.defaultLineWidth
                color: config.defaultLineColor
            }
        }
    }
    //输出变量引脚
    Repeater {
        model: bindData.Connectors
        Rectangle {
            id: c_outrect
            visible: model.Type === "OUT"
            width: (config.defaultENENOWidth - config.defaultLineWidth) * 2
            height: config.defaultConnectorHeight
            anchors {
                left: c_bl.right
                leftMargin: config.defaultENENOWidth / 2
            }
            y: config.defaultLineHeightOffset + (model.YOffset + 1)
               * (config.defaultConnectorHeight + config.defaultConnectorMargin)
            border {
                width: 1
                color: config.defaultVaiableBorderColor
            }
            color: config.defaultVaiableBackgroundColor
            MouseArea {
                anchors.fill: parent
                acceptedButtons: Qt.LeftButton | Qt.RightButton
                hoverEnabled: true
                onClicked: {
                    mainControl.selectID = bindData.LevelInfo
                    if (mouse.button === Qt.RightButton) {
                        var positionInRoot = mapToItem(mainControl,
                                                       mouse.x, mouse.y)
                        mainControl.showMenu(positionInRoot.x,
                                             positionInRoot.y, model.Type,
                                             model.PinId)
                    }
                }
                onEntered: {
                    otoolTip_DataType.visible = true
                }
                onExited: {
                    otoolTip_DataType.visible = false
                }
            }
            ToolTip {
                id: otoolTip_DataType
                parent: c_outrect
                y: -35
                delay: 500
                text: model.DataType
                timeout: 3000
            }
            //变量名
            Text {
                id: c_outtext
                anchors.centerIn: parent.Center
                font.pixelSize: config.fontPixelSize
                height: parent.height
                width: parent.width
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                text: model.LabelName
                color: config.defaultVaiableNameColor
            }
            //变量值
            Text {
                id: c_outvalue
                anchors {
                    left: c_outrect.right
                    leftMargin: 0
                    top: c_outrect.top
                    topMargin: 0
                }
                font.pixelSize: config.fontPixelSize
                height: parent.height
                width: parent.width
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                text: model.Value
                color: config.defaultVaiableValueColor
            }
            //尾线
            Rectangle {
                anchors {
                    right: c_outtext.left
                    rightMargin: 0
                    top: c_outtext.top
                    topMargin: c_outtext.height / 2
                }
                width: config.defaultENENOWidth / 2
                height: config.defaultLineWidth
                color: config.defaultLineColor
            }
        }
    }

    function showConnector(type) {
        if (type === "EN" || type === "ENO" || type === "IN" || type === "OUT")
            return true

        return false
    }

    function getMaxConnectorNumber() {
        if (bindData.inputPinNum > bindData.outputPinNum) {
            return bindData.inputPinNum + 1
        } else {
            return bindData.outputPinNum + 1
        }
    }
}
