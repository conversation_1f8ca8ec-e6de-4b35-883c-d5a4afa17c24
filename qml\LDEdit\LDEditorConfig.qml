﻿pragma Singleton

import QtQuick 2.15

QtObject {
    //默认字体大小
    property int fontPixelSize: 12
    //默认偏移长度
    property int defaultMargin: 20
    //默认注释框宽度
    property int defaultDescWidth: 600
    //默认注释框高度
    property int defaultDescHeight: 50

    //图元默认宽度
    property int defaultImageWidth: 99
    //图元默认高度
    property int defaultImageHeight: 54
    //功能与功能块默认宽度
    property int defaultBlockWidth: 190

    //默认线条宽度
    property int defaultLineWidth: 1
    //默认线条在图片中的高度偏移
    property int defaultLineHeightOffset: 26
    //默认块引脚高度
    property int defaultConnectorHeight: 20
    //默认块引脚间距
    property int defaultConnectorMargin: 2

    //或 结束块引脚宽度
    property int defaultENENOWidth: 25

    property color defaultNetworkDescColor: "#097B44"
    property color defaultNetworkDescBackgroundColor: "transparent"

    property color defaultBlockDescColor: "#097B44"
    property color defaultBlockDescBackgroundColor: "transparent"

    //默认线条的一组颜色
    property color defaultLineColor: "#333333"
    property color defautlLineTrueColor: "green"
    property color defautlLineFalseColor: "red"

    //引脚变量一组颜色
    property color defaultVaiableNameColor: "#0087DE"
    property color defaultVaiableValueColor: "black"
    property color defaultVaiableAddressColor: "#333333"
    property color defaultVaiableBorderColor: "#A5A5A5"
    property color defaultVaiableBackgroundColor: "transparent"
}
