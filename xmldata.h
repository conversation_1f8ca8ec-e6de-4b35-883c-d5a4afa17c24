﻿#ifndef XMLDATACFC_H
#define XMLDATACFC_H
#include <QList>
#include <QString>

//输入或者输出类型（单条数据）
struct XMLIOPutCFC
{
    int index;
    QString name;
    QString type;
    QString initValue;//初始值
    QString defaultValue;//默认值
    XMLIOPutCFC()
    {
        index = 0;
        name = QString();
        type = QString();
        initValue = QString();
        defaultValue = "";
    }
    bool operator==(const XMLIOPutCFC& other)
    {
        if (this->name != other.name)
        {
            return false;
        }
        if (this->type.toUpper() != other.type.toUpper())
        {
            return false;
        }
        return true;
    }

};
//功能或者功能块数据（根据枚举值来判断）
struct XMLFuncitionCFC
{
    enum FUNCITIONTYPE
    {
        FUNCITION,
        FUNCITION_BLOCK
    };
    QString type;
    QString outType;
    QList<XMLIOPutCFC>inPut;
    QList<XMLIOPutCFC>outPut;
    QString mode;

    FUNCITIONTYPE funcitionType;
    XMLFuncitionCFC()
    {
        type = QString();
        outType = QString();
    }

    QString ToString()
    {
        QString str;
        str += "{funcitionType:" + QString(funcitionType);
        str += ",type:" + type;
        str += ",outType:" + outType;
        str += ",mode:" + mode;
        str += ",inPut size:" + QString(inPut.size());
        str += ",outPut size:" + QString(outPut.size());
        str += "}";
        return str;
    }
};


#endif // XMLDATA_H
