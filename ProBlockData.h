﻿// #ifndef PROBLOCKDATA_H
// #define PROBLOCKDATA_H

// #include <QObject>
// #include <qmap.h>
// #include "xmldata.h"

// class ProBlockData : public QObject
// {
//     Q_OBJECT
// public:
//     explicit ProBlockData(QObject *parent = nullptr);
// public:
//     static void setProjectPathAndDirectory(QString dir, QString path);

//     //刷新整个工程功能块数据
//     static void reflushProFuncBlockListData();

//     static QMap<QString, QMap<QString, XMLFuncition>> &getAllFuncBlockListData();

//     static bool checkBlockInfoExit(QString coreName, XMLFuncition info);

//     //读取ptt数据(属于整个工程)  功能块
//     static QMap<QString, XMLFuncition> readPttFunctionBlockData();

//     //读取ptt数据(属于整个工程)  功能 项目路径/$GEN$/下
//     static QMap<QString, XMLFuncition> readPttFunctionData();

//     //读取固件数据 应用目录的/Function目录下
//     static QMap<QString, XMLFuncition> readFirmwareData();

//     //读取当前激活core的fb数据
//     static QMap<QString, XMLFuncition>readFunctionBlockData();

//     //获取inc文件路径
//     static QString getIncPath(QString proPath, QString coreName);

//     //获取optimization 核心的硬件配置文件 项目$ENV$目录激活核心目录下
//     static QString getOptimization(QString proPath, QString coreName);

//     //获取工程当前激活的核
//     static QString getActivateCore(QString proPath);

//     static QStringList readTextData(QString path); //读取文件

//     //读取整个工程块数据
//     static QMap<QString, QMap<QString, XMLFuncition>>readProBlockData();

//     //初始化整个工程的功能块列表,返回空列表
//     static QMap<QString, QMap<QString, XMLFuncition>> initProFuncBlockListData();

//     static QStringList readPttTextData(QString dirpath);

//     //获取varq下所有core and coretype
//     static QStringList getProAllCore();
//     static QMap<QString, QString> getProAllCoreAndType();

//     //读取core的type
//     static QString getCoreType(QString proPath, QString coreName);

//     //获取modules的所有类型注意在xml里去找类型  key:moduleType  value:folderName
//     static QMap<QString, QString> getModulesType();

//     static XMLRes readSourcesXML(QString configName); //读取新建工程xml文件

//     //获取硬件配置xml里面的硬件类型
//     static QString getHardwareName(QString path);
//     //返回function的数据，type=1：功能名称和类型 type=0：变量名称和类型
//     static QStringList returnFunctionDatas(QString datas, bool type);

//     //读取module 的ini 查询键值对
//     static void checkModuleMode(QString iniPath, QMap<QString, XMLFuncition> &blockData);

//     //根据block的名字获取键值对
//     static QMap<QString, QString>checkKeyValue(QStringList blobckNames, QString iniPath);

//     //根据键值对的值获取mode的类型
//     static QMap<QString, QString>checkModeData(QMap<QString, QString>blockKeyValue, QString iniPath);

//     //根据isn 判断mode
//     static QString judgeISN(bool bInitMode, bool bSystemMode, bool bNormalMode);

//     //在功能块的cfc里面读取mode属性
//     static QString readFuncBlockMode(QString cfcFileName);

//     //比较两个函数是否一致
//     static bool compareXmlFunction(XMLFuncition function1, XMLFuncition function2);

//     //接口使用
//     static QMap<QString, XMLFuncition>readPttFunction(QString path);
//     static QMap<QString, XMLFuncition> readFunction(QString path); //读取功能文件
//     static QMap<QString, XMLFuncition> readPttFunctionAndBlock(QString path); //读取功能文件
//     static QMap<QString, XMLFuncition> readFunctionBlock(QString path); //读取功能块文件

//     static QStringList returnFunctionBlock(QString datas, bool type);//返回functionblock的数据，type=1：功能块名称
//     //type=0：输入输出变量名称，类型和默认值

// private:
//     //缓存整个工程的功能块列表的数据 [coreName] ppt firmware
//     static QMap<QString, QMap<QString, XMLFuncition>> m_mapFuncBlockListData;

//     static QString m_projectDirectory;//项目根目录
//     static QString m_projectPath;//项目.VARQ文件路径

// signals:

// };

// #endif // PROBLOCKDATA_H
