﻿import QtQuick 2.15

//LD 线圈块
Item {
    id: control

    //主控件
    property var mainControl
    //配置
    property var config: LDEditorConfig
    //绑定的数据
    property var bindData

    width: img.width
    height: config.defaultConnectorHeight + img.height + config.defaultConnectorMargin

    //图片
    Image {
        id: img
        anchors {
            left: parent.left
            leftMargin: 0
            top: parent.top
            topMargin: config.defaultConnectorHeight + config.defaultConnectorMargin
        }
        width: config.defaultImageWidth
        height: config.defaultImageHeight
        source: "icons/ld_return.png"
        fillMode: Image.Stretch
        //EN ENO引脚
        Repeater {
            model: bindData.Connectors
            Rectangle {
                id: enoline
                visible: model.Type === "EN" || model.Type === "ENO"
                width: config.defaultImageWidth / 2 - 14
                height: config.defaultLineWidth
                x: model.Type === "EN" ? 0 : config.defaultImageWidth / 2 + 14
                y: config.defaultLineHeightOffset
                color: mainControl.isOnline ? (model.Value ? config.defaultLineTrueColor : config.defaultLineFalseColor) : config.defaultLineColor
                //引脚的标签名称
                Text {
                    anchors {
                        horizontalCenter: parent.horizontalCenter
                        top: parent.top
                        topMargin: -config.fontPixelSize
                    }
                    visible: config.isShowLabelName
                    font.pixelSize: config.fontPixelSize
                    text: model.LabelName
                }
            }
        }
    }
}
