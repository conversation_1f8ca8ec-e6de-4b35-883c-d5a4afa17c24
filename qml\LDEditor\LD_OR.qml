import QtQuick 2.15
import QtQuick.Controls 2.15

Item {
    // 主控件
    property var mainControl
    // 当前所在的网络组件
    property var netWorkControl
    // 配置
    property var config: LDEditorConfiger
    // 块数据
    property var blockData
    // 记录当前选中的网络中对应的start块元件序号
    property int startBlockNumber
    // 当前所在的网络号
    property int netWorkNumber

    width: 1
    height: blockData.Height * config.cellHeight
    visible: isTest

    Rectangle {
        id: or_block_rect
        width: 12
        height: 12
        x: -(width / 2 + config.defaultLineWidth / 2)
        anchors.verticalCenter: parent.verticalCenter
        anchors.verticalCenterOffset: -1
        color: "#0000ff"
    }
    Text {
        id: or_blcok_pos
        visible: isTest
        text: "(" + blockData.XPos + "," + blockData.YPos + ")"
        font.pixelSize: config.fontPixelSize
        x: or_block_rect.x + (or_block_rect.width - width) / 2
        y: or_block_rect.y - height - 2
    }
    Text {
        visible: isTest
        text: blockData.Number
        font.pixelSize: config.fontPixelSize
        x: or_block_rect.x + (or_block_rect.width - width) / 2
        y: or_block_rect.y - height - 2 - or_blcok_pos.height
    }
}