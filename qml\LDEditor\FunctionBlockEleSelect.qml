import QtGraphicalEffects 1.15
import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/qml/control/common"
import "qrc:/qml/control/table"

Popup {
    id: control
    property string deviceName: ""
    // 文件名
    property string owned: ""
    // 文件分类
    property string type: ""

    QkTableData {
        id: tableData
        headerRoles: ["名称", "类型"]
        rowDatas: listModel
    }

    ListModel {
        id: listModel
    }

    Rectangle {
        id: table
        width: parent.width - 20
        anchors.left: parent.left
        anchors.top: parent.top
        anchors.topMargin: 5
        anchors.leftMargin: 10
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        color: "#F0F0F0"

        QkTableGird {
            id: tableGrid
            anchors.top: parent.top
            needsort: false
            showCheckColumn: true
            width: parent.width
            height: parent.height - 40
            tableData: tableData
            tableview.delegate: QkTableRow {
                id: tableRow
                parentControl: tableGrid.tableview
                width: tableGrid.width - 2
                rowObj: model
                widthList: tableGrid.widthList
                xList: tableGrid.xList
                onCheckedChanged: {
                    tableGrid.tableData.check(index, checked)
                }

                QkLabel {
                    text: tableRow.rowObj ? (String(
                                                 tableRow.rowObj["Name"])) : ""
                    x: tableGrid.xList[1]
                    width: tableGrid.widthList[1]
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                QkLabel {
                    text: tableRow.rowObj ? (String(
                                                 tableRow.rowObj["Type"])) : ""
                    x: tableGrid.xList[2]
                    width: tableGrid.widthList[2]
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }

        QkButton {
            anchors.top: tableGrid.bottom
            anchors.topMargin: 5
            anchors.right: parent.horizontalCenter
            anchors.rightMargin: 70
            width: 70
            height: 30
            text: "取消"
            onClicked: {
                control.close()
            }
        }

        QkButton {
            anchors.top: tableGrid.bottom
            anchors.topMargin: 5
            anchors.left: parent.horizontalCenter
            anchors.leftMargin: 70
            width: 70
            height: 30
            text: "添加"
            onClicked: {
                const modelDatas = []

                for(let mIndex = 0; mIndex < listModel.count; mIndex++)
                {
                    const modelData = listModel.get(mIndex)
                    if(modelData.isChecked)
                    {
                        modelDatas.push(modelData)
                    }
                }

                // 一次只能选中一条数据
                if(modelDatas.length > 1)
                {
                    msg_popup.msg = "最多只能选择一条数据！"
                    msg_popup.open()
                    msg_timer.start()

                }
                else if(modelDatas.length === 0)
                {
                    msg_popup.msg = "请选择一条数据进行操作！"
                    msg_popup.open()
                    msg_timer.start()
                }
                else
                {
                    // 块元件类型
                    let type = modelDatas[0].Type.toLowerCase()

                    if(type === "function")
                    {
                        type = "FUNC"
                    }
                    else if(type === "functionblock")
                    {
                        type = "FB"
                    }
                    else if(type === "advance")
                    {
                        type = "ADVANCE"
                    }

                    const funcBlockData = {
                        "Name": modelDatas[0].Name,
                        "Type": type
                    }
                    
                    if(currentOpenMenu === "NETWORK")
                    {
                        addBlockComponent(startBlockNumber, funcBlockData)
                    }
                    else if(currentOpenMenu === "BLOCK")
                    {
                        addBlockComponent(currentSelectBolckData.Number, funcBlockData)
                    }
                    
                    control.close()
                }
            }
        }
    }

    background: Rectangle {
        color: "#f0f0f0"
        radius: 8
    }

    Popup {
        id: msg_popup
        width: 180
        height: 50
        anchors.centerIn: parent
        closePolicy: Popup.NoAutoClose
        opacity: 0.0

        property string msg: ""

        background: Rectangle {
            color: "#f0f0f0"
            radius: 8
        }

        Text {
            text: msg_popup.msg
            font.pixelSize: 14
            anchors.verticalCenter: parent.verticalCenter
        }

        Behavior on opacity {
            NumberAnimation { duration: 300 }
        }

        onVisibleChanged: {
            if (visible)
            {
                opacity = 1.0
            }
        }

    }

    Timer {
        id: msg_timer
        interval: 2000
        repeat: false
        onTriggered: {
            msg_popup.opacity = 0.0
            Qt.callLater(() => {
                msg_popup.close()
            })
        }
    }

    onOpened: {
        init()
    }

    function init()
    {
        listModel.clear()
        const funcBlocks = serviceInterface.getAllFunctionAndBlock(control.deviceName)
        const firmFunctionBlock = funcBlocks["firmfunctionblock"]
        const firmFunction = funcBlocks["firmfunction"]
        // const userFBs = funcBlocks["pptall"]
        const advances = funcBlocks["advance"]

        //添加用户定义的功能与功能块
        // for (let uIndex = 0; uIndex < userFBs.length; uIndex++)
        // {
        //     listModel.append({
        //                          "Name": userFBs[uIndex].name,
        //                          "Type": "PPTall"
        //                      })
        // }
        //添加功能块
        for (let fbIndex = 0; fbIndex < firmFunctionBlock.length; fbIndex++)
        {
            listModel.append({
                                 "isChecked": false,
                                 "isSelected": false,
                                 "isAlternate": false,
                                 "Name": firmFunctionBlock[fbIndex].name,
                                 "Type": "FunctionBlock"
                             })
        }
        //添加功能
        for (let fIndex = 0; fIndex < firmFunction.length; fIndex++)
        {
            listModel.append({
                                 "isChecked": false,
                                 "isSelected": false,
                                 "isAlternate": false,
                                 "Name": firmFunction[fIndex].name,
                                 "Type": "Function"
                             })
        }
        // 添加advance
        for (let advIndex = 0; advIndex < advances.length; advIndex++)
        {
            listModel.append({
                                 "isChecked": false,
                                 "isSelected": false,
                                 "isAlternate": false,
                                 "Name": advances[advIndex].name,
                                 "Type": "Advance"
                             })
        }
    }
}
