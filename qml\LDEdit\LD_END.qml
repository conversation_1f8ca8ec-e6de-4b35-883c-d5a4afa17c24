﻿import QtQuick 2.15

//LD 结束块主要包含线圈和与块
Rectangle {
    id: control

    //主控件
    property var mainControl
    //配置
    property var config: LDEditorConfig
    //线条起点的偏移量
    property int lineOffset: config.defaultLineHeightOffset + config.defaultConnectorHeight
                             + config.defaultConnectorMargin
    //绑定的数据
    property var bindData

    //子模块的最大宽度
    property int childwidth: 0

    property int vlineHeight: 0

    property color tailColor: config.defaultLineColor

    width: childwidth + config.defaultENENOWidth

    color: "transparent"

    Repeater {
        model: bindData.Connectors
        //EN引脚
        Rectangle {
            visible: model.Type === "EN"
            width: config.defaultENENOWidth
            height: config.defaultLineWidth
            anchors {
                left: control.left
                leftMargin: 0
                top: control.top
                topMargin: control.lineOffset
            }
            color: mainControl.isOnline ? (model.Value ? config.defaultLineTrueColor : config.defaultLineFalseColor) : config.defaultLineColor
        }
    }
    Repeater {
        model: bindData.Connectors
        //纵向线
        Rectangle {
            visible: model.Type === "EN"
            anchors {
                left: control.left
                leftMargin: config.defaultENENOWidth
                top: control.top
                topMargin: control.lineOffset
            }
            width: config.defaultLineWidth
            height: control.vlineHeight
            color: mainControl.isOnline ? (model.Value ? config.defaultLineTrueColor : config.defaultLineFalseColor) : config.defaultLineColor
        }
    }
    //没有变量与注释
    Column {
        id: c_bl
        anchors {
            left: parent.left
            leftMargin: config.defaultENENOWidth
            top: parent.top
            topMargin: 0
        }
        Repeater {
            model: bindData.Blocks
            LDBlock {
                id: bl

                mainControl: control.mainControl
                bindData: model
                function reSize() {
                    var t = 0
                    var tmpwidth = 0
                    var tmpheight = 0
                    for (var i = 0; i < c_bl.children.length; i++) {
                        if (c_bl.children[i] instanceof LDBlock) {
                            if (t === 0) {
                                tmpwidth = c_bl.children[i].width
                                tmpheight = c_bl.children[i].height
                            } else {
                                if (tmpwidth < c_bl.children[i].width)
                                    tmpwidth = c_bl.children[i].width

                                if (i === c_bl.children.length - 2) {
                                    control.vlineHeight = tmpheight + config.defaultLineWidth
                                }
                                tmpheight += c_bl.children[i].height
                            }
                            t++
                        }
                    }
                    control.childwidth = tmpwidth
                    control.height = tmpheight
                }
                Component.onCompleted: {
                    reSize()
                }
                onHeightChanged: {
                    reSize()
                }
                onWidthChanged: {
                    reSize()
                }
                //尾线
                Rectangle {
                    id: tailLine
                    anchors {
                        left: bl.right
                        leftMargin: 0
                        top: bl.top
                        topMargin: control.lineOffset
                    }
                    width: control.childwidth - bl.width
                    height: config.defaultLineWidth
                    color: control.tailColor
                }
            }
        }
    }
}
