// #include "ld2poe.h"
// #include <QMap>
// #include <QList>
// #include <QDebug>
// #include <QStringList>

// QString transform(const QString &in)
// {
//     QString res_out;
//     QString res1;
//     QString res2;
//     QString res3;
//     QString key;
//     QString once;
//     int log = 0;
//     int num_n = 0;

//     QStringList lines = in.split("\n");

//     // 处理空行
//     foreach (QString line, lines)
//     {
//         QString trimmedLine = line.trimmed();
//         if (!trimmedLine.isEmpty())
//         {
//             res1 += line + "\n";
//         }
//     }

//     lines = res1.split("\n");

//     // 处理LD和ST关键字
//     foreach (QString one, lines)
//     {
//         QStringList list_one = one.trimmed().split("\t");
//         if (key.isEmpty() && list_one.size() > 1 && list_one.at(0) == "LD" && list_one.at(1).contains("_tmp"))
//         {
//             key = list_one.at(1).left(list_one.at(1).length() - 4);
//         }
//         else if (!key.isEmpty() && list_one.size() > 1 && list_one.at(0) == "ST" && list_one.at(1) == key)
//         {
//             key = "";
//             res2 += "\n";
//         }
//         res2 += one + "\n";
//     }

//     lines = res2.split("\n");

//     // 处理LD和ST之间的关系
//     foreach (QString line, lines)
//     {
//         QString trimmedLine = line.trimmed();
//         if (!trimmedLine.isEmpty())
//         {
//             QStringList parts = trimmedLine.split("\t");
//             if (parts[0] == "ST" && log == 0)
//             {
//                 log = 1;
//                 once = parts[1];
//             }
//             else if (parts[0] == "LD" && log == 1 && once == parts[1])
//             {
//                 continue;
//             }
//             else
//             {
//                 log = 0;
//             }
//             num_n = 0;
//             res3 += line + "\n";
//         }
//         else
//         {
//             num_n++;
//             if (num_n == 2)
//             {
//                 continue;
//             }
//         }
//     }

//     int i = 0;
//     lines = res3.split("\n");

//     // 处理多余空行
//     foreach (QString one, lines)
//     {
//         QString trimmedLine = one.trimmed();
//         if (trimmedLine.isEmpty())
//         {
//             i++;
//             if (i > 3)
//             {
//                 continue;
//             }
//         }
//         else
//         {
//             i = 0;
//         }
//         res_out += one + "\n";
//     }

//     return res_out;
// }

// // 做dfs获取IL代码段 依据每个network进行操作
// // 顺带便利结构创建数据对象 创建的临时结果对象放在 setConnectorName里面
// // 对象分为两个基本结构 or 树 或者 and 树， or 里面 有and and 里面有or， 这里有些不美观的部分 就会出现 and and 的部分，导致递归不完美
// QString LD2POE::dfsFromLdblock(const QList<QSharedPointer<LDBlock>> &blocks, QString blockTypeFather, QString varOut)
// {
//     QString res = "";

//     for (int i = 0; i < blocks.size(); ++i)
//     {
//         QString blockType = blocks[i]->BlockType;
//         QList<LDConnector> connectors = blocks[i]->connectors;
//         QString varIn = connectors[0].LabelName;

//         if (blockTypeFather == "OR" && blockType == "AND")
//         {
//             // qDebug() << "OR AND  " << connectors[1].LabelName + "_tmp";
//             res += "\n" + dfsFromLdblock(blocks[i]->blocks, "AND", connectors[1].LabelName + "_tmp");
//             if (connectors[1].LabelName != "end")
//             {
//                 res += "\nLD\t" + connectors[1].LabelName + "_tmp\nST\t" + connectors[1].LabelName + "\n";
//             }
//         }
//         else if (blockTypeFather == "AND" && blockType == "AND")
//         {
//             //  qDebug() << "AND AND  " << "";
//             res += "\n" + dfsFromLdblock(blocks[i]->blocks, "AND", "");
//         }
//         else if (blockTypeFather == "AND" && blockType == "OR")
//         {
//             //  qDebug() << "AND OR  " << connectors[1].LabelName + "_tmp";
//             setConnectorName.insert(connectors[1].LabelName + "_tmp");
//             res += "\n" + dfsFromLdblock(blocks[i]->blocks, "OR", connectors[1].LabelName + "_tmp");
//             if (connectors[1].LabelName != "end")
//             {
//                 res += "\nLD\t" + connectors[1].LabelName + "_tmp\nST\t" + connectors[1].LabelName + "\n";
//             }
//         }
//         else
//         {
//             if (blockType != "CONTACT")
//             { // Coil
//                 res += "\nLD\t" + (varIn == "start" ? "TRUE" : varIn) + "\t(*ld'and'reload_result*)";
//             }
//             if (blockType == "CONTACT")
//             { // Contact
//                 res += "\n";
//                 res += (blocks[i]->Negate ? "LDN\t" : "LD\t") + connectors[1].LabelName;
//             }
//             else if (blockType == "COIL")
//             { // Coil
//                 res += "\n";
//                 res += (blocks[i]->Negate ? "STN\t" : "ST\t") + connectors[1].LabelName;
//             }
//             else if (blockType == "Set1")
//             { // Set
//                 res += "\nS\t" + connectors[1].LabelName + "\nLD\t" + connectors[1].LabelName;
//             }
//             else if (blockType == "Set0")
//             { // Reset
//                 res += "\nR\t" + connectors[1].LabelName + "\nLD\t" + connectors[1].LabelName;
//             }
//             else if (blockType == "JMP")
//             { // Jump
//                 res += "\nJMPC\t" + connectors[1].LabelName;
//             }
//             else if (blockType == "RETURN")
//             { // 退出块，操作数的信号状态与调用程序块的使能输出ENO相对应
//                 res += "\nRETC";
//             }
//             else if (blockType == "FUNCTION" || blockType == "FUNCTIONBLOCK")
//             { // 功能  这里要限制引脚顺序，如乱序徐添加额外的信息进行排序
//                 //  ST		_ldABS_INT_1.EN						(*ld'fb'en*)
//                 // 	CAL		_ldABS_INT_1(
//                 // 					IN		:= int1				(*ld'fb'in*)
//                 // 				|	int2	:= OUT)				(*ld'fb'out*)
//                 // 	LD			_ldABS_INT_1.ENO				(*ld'fb'eno'absolute*)
//                 QString tmp = "";
//                 QString typeOld = "";
//                 for (int j = 0; j < connectors.size(); ++j)
//                 {
//                     qDebug() << connectors.at(j).Type;
//                     if (connectors.at(j).Type == "EN")
//                     {
//                         tmp += "\nST\t" + blocks.at(i)->InstanceName + ".EN\t(*ld'fb'en*)\n";
//                     }
//                     else if (connectors.at(j).Type == "IN")
//                     {
//                         if (typeOld == "IN")
//                         {
//                             tmp += ",\t(*ld'fb'in*)\n";
//                         }
//                         else if (blockType == "FUNCTION")
//                         {
//                             tmp += "CALC\t" + blocks.at(i)->InstanceName + "(\n";
//                         }
//                         else
//                         {
//                             tmp += "CAL\t" + blocks.at(i)->InstanceName + "(\n";
//                         }
//                         tmp += connectors.at(j).Name + "\t:=\t" + connectors.at(j).LabelName;
//                     }
//                     else if (connectors.at(j).Type == "OUT")
//                     {
//                         if (typeOld == "IN")
//                         {
//                             tmp += "\t(*ld'fb'in*)\n|";
//                         }
//                         else
//                         {
//                             tmp += ",\t(*ld'fb'out*)\n";
//                         }
//                         tmp += connectors.at(j).Name + "\t:=\t" + connectors.at(j).LabelName;
//                     }
//                     else if (connectors.at(j).Type == "ENO")
//                     {
//                         if (blockType == "FUNCTIONBLOCK")
//                         {
//                             tmp += ")\t(*ld'fb'out*)\nLD\t" + blocks.at(i)->InstanceName + ".ENO\t(*ld'fb'eno'absolute*)";
//                         }
//                         else if (blockType == "FUNCTION")
//                         {
//                             tmp += ")\t(*ld'fb'out*)\n";
//                         }
//                     }
//                     typeOld = connectors.at(j).Type;
//                 }
//                 res += tmp;
//             }

//             if (connectors[connectors.size() - 1].LabelName != "end" || blockType == "CONTACT")
//             { // varOut != "end_tmp"
//                 if (blockTypeFather == "OR")
//                 {
//                     if (i == 0)
//                     {
//                         res += "\nST\t" + varOut + "\t(*ld'or*)";
//                     }
//                     else
//                     {
//                         res += "\nOR\t" + varOut + "\t(*ld'or*)";
//                         if (i < blocks.size() - 1)
//                         {
//                             res += "\nST\t" + varOut + "\t(*ld'or*)";
//                         }
//                         else
//                         {
//                             if (varIn != "start" && blockType != "FUNCTION" && blockType != "FUNCTIONBLOCK")
//                             {
//                                 res += "\nAND\t" + varIn + "\t(*ld'and*)";
//                             }
//                             res += "\nST\t" + varOut + "\t(*ld'or*)";
//                         }
//                     }
//                 }
//                 else if (blockTypeFather == "AND")
//                 {
//                     if (varIn != "start" && (connectors[connectors.size() - 1].LabelName != "end" || blockType == "CONTACT") && blockType != "FUNCTION" && blockType != "FUNCTIONBLOCK")
//                     {
//                         res += "\nAND\t" + connectors[0].LabelName + "\t(*ld'and*)";
//                     }

//                     if (varOut.isEmpty() || i < blocks.size() - 1)
//                     {
//                         res += "\nST\t" + connectors[connectors.size() - 1].LabelName + "\t(*ld'and*)";
//                     }
//                     else
//                     {
//                         QStringList tmp = blocks[i]->LevelInfo.split("_");
//                         if (tmp[tmp.size() - 2] != "1")
//                         {
//                             res += "\nOR\t" + varOut + "\t(*ld'or*)";
//                         }
//                         res += "\nST\t" + varOut + "\t(*ld'and*)";
//                     }
//                 }
//             }
//         }
//     }

//     return transform(res);
// }
