﻿import QtQuick 2.15

//LD网络
Rectangle {
    id: control

    //主控件
    property var mainControl
    //配置
    property var config: LDEditorConfig
    //绑定的数据
    property var bindData

    width: mainControl.width

    height: 20 + 2 * config.defaultDescHeight + blockrow.height

    color: "transparent"

    //    border {
    //        width: 1
    //        color: "gray"
    //    }

    //    Keys.enabled: true // 不设置按键使能，获取不了按键事件
    //    focus: false // 不设置焦点，获取不了键盘事件
    //    Keys.onPressed: {
    //        console.log("key:" + event.key, bindData.SortNumber)
    //    }

    //右键菜单
    MouseArea {
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        drag.target: mainControl.mainroot
        drag.axis: Drag.XAndYAxis
        drag.maximumX: 10000
        drag.minimumX: -10000
        drag.minimumY: -10000
        drag.maximumY: 10000
        onClicked: {
            mainControl.selectID = bindData.LevelInfo
            if (mouse.button === Qt.RightButton) {
                var positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                mainControl.showMenu(positionInRoot.x, positionInRoot.y,
                                     "Network")
            }
        }
        onWheel: {
            if (wheel.angleDelta.y > 0) {
                root.scale += 0.1
                if (root.scale >= 2)
                    root.scale = 2
            } else {
                root.scale -= 0.1
                if (root.scale <= 0.2)
                    root.scale = 0.2
            }
        }
    }

    //段落标识区
    Rectangle {
        id: mainDesc
        anchors {
            left: parent.left
            leftMargin: 0
            top: parent.top
            topMargin: 0
        }
        border {
            width: 1
            color: "gray"
        }
        width: mainControl.width - config.defaultMargin * 2
        height: config.defaultMargin
        color: config.defaultNetworkDescBackgroundColor
        //网络序号
        Text {
            id: txt_sortnumber
            anchors {
                verticalCenter: parent.verticalCenter
                left: parent.left
                leftMargin: config.defaultMargin
            }
            width: 60
            height: parent.height
            font.pixelSize: config.fontPixelSize
            text: bindData.SortNumber + "段落|"
            color: config.defaultNetworkDescColor
            horizontalAlignment: Text.right
            verticalAlignment: Text.AlignVCenter
        }
        //标签
        Row {
            id: txt_label
            anchors {
                verticalCenter: parent.verticalCenter
                left: txt_sortnumber.right
                leftMargin: 0
            }
            height: parent.height
            width: 140
            Text {
                font.pixelSize: config.fontPixelSize
                text: "标签:"
                width: 40
                height: parent.height
                color: config.defaultNetworkDescColor
                horizontalAlignment: Text.AlignLeft
                verticalAlignment: Text.AlignVCenter
            }
            TextInput {
                font.pixelSize: config.fontPixelSize
                width: 100
                maximumLength: 15
                height: parent.height
                text: bindData.LabelName
                horizontalAlignment: Text.AlignLeft
                verticalAlignment: Text.AlignVCenter
                color: config.defaultNetworkDescColor
                onEditingFinished: {
                    bindData.LabelName = text
                    updateLabelNameAndComment()
                }
            }
        }
        Text {
            id: txt_desc
            anchors {
                verticalCenter: parent.verticalCenter
                left: txt_label.right
                leftMargin: config.defaultMargin / 2
            }
            width: 40
            font.pixelSize: config.fontPixelSize
            text: "注释:"
            color: config.defaultNetworkDescColor
            horizontalAlignment: Text.right
        }
        //网络注释
        Rectangle {
            anchors {
                verticalCenter: parent.verticalCenter
                left: txt_desc.right
                leftMargin: 0
            }
            color: "transparent"
            height: parent.height
            width: parent.width - txt_sortnumber.width - txt_label.width - txt_desc.width
            //文本编辑
            TextInput {
                id: editComment
                width: parent.width
                height: parent.height
                maximumLength: 100
                text: control.bindData.Comment
                font.pixelSize: config.fontPixelSize
                color: config.defaultNetworkDescColor
                verticalAlignment: Text.AlignVCenter
                onEditingFinished: {
                    bindData.Comment = text
                    updateLabelNameAndComment()
                }
            }

            //滚动注释栏（暂不用)
            //            Flickable {
            //                id: flickText
            //                anchors.fill: parent
            //                contentWidth: editComment.paintedWidth
            //                contentHeight: editComment.paintedHeight
            //                clip: true
            //                function ensureVisible(r) {
            //                    if (contentX >= r.x)
            //                        contentX = r.x
            //                    else if (contentX + width <= r.x + r.width)
            //                        contentX = r.x + r.width - width
            //                    if (contentY >= r.y)
            //                        contentY = r.y
            //                    else if (contentY + height <= r.y + r.height)
            //                        contentY = r.y + r.height - height
            //                }
            //                TextEdit {
            //                    id: editComment
            //                    width: flickText.width
            //                    text: control.bindData.Comment
            //                    font.pixelSize: config.fontPixelSize
            //                    color: config.defaultNetworkDescColor
            //                    textFormat: TextEdit.PlainText
            //                    wrapMode: TextEdit.Wrap
            //                    onCursorRectangleChanged: flickText.ensureVisible(
            //                                                  cursorRectangle)
            //                    onEditingFinished: {
            //                        bindData.Comment = text
            //                        updateLabelNameAndComment()
            //                    }
            //                }
            //            }
        }
    }

    //左母线 右母线
    Rectangle {
        anchors {
            right: blockrow.left
            rightMargin: 0
            top: blockrow.top
            topMargin: 0
        }
        width: config.defaultLineWidth * 2
        height: blockrow.height
        color: config.defaultLineColor
    }
    Rectangle {
        anchors {
            left: blockrow.right
            leftMargin: 0
            top: blockrow.top
            topMargin: 0
        }
        width: config.defaultLineWidth * 2
        height: blockrow.height
        color: config.defaultLineColor
    }

    //块
    Row {
        id: blockrow
        anchors {
            left: parent.left
            leftMargin: 0
            top: mainDesc.bottom
            topMargin: config.defaultMargin / 2
        }
        spacing: 0
        Repeater {
            model: control.bindData.Blocks
            LDBlock {
                id: bl
                mainControl: control.mainControl
                bindData: model
                function reSize() {
                    var t = 0
                    var tempwidth = 0
                    var tempheight = 0
                    for (var i = 0; i < blockrow.children.length; i++) {
                        if (blockrow.children[i] instanceof LDBlock) {
                            if (t === 0) {
                                tempwidth = blockrow.children[i].width
                                tempheight = blockrow.children[i].height
                            } else {
                                tempwidth += blockrow.children[i].width
                                if (tempheight < blockrow.children[i].height)
                                    tempheight = blockrow.children[i].height
                            }
                            t++
                        }
                    }
                    blockrow.width = tempwidth
                    blockrow.height = tempheight
                }
                Component.onCompleted: {
                    reSize()
                }
                onWidthChanged: {
                    reSize()
                }
                onHeightChanged: {
                    reSize()
                }
            }
        }
    }
    function updateLabelNameAndComment() {
        var re = ldManage.modifyNetwork(
                    mainControl.fileKey,
                    parseInt(bindData.SortNumber), bindData.LabelName,
                    bindData.Comment)
        if (re === "") {
            mainControl.getDataBind()
        } else {
            console.log("Network updateLabelName&Comment error:", re)
        }
    }
}
