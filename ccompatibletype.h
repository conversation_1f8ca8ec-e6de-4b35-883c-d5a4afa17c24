﻿#ifndef CCOMPATIBLETYPE_H
#define CCOMPATIBLETYPE_H

#include <QDomDocument>
#include <QDomElement>
#include <QDomNode>
#include <QList>
#include <QFile>
#include <QTextStream>

//来自Common下,将默认应用目录修改为传入指定目录
//用于管理数据类型兼容配置文件在 TypeTranslate.h中调用
class CCompatibleType
{
public:
    //获取当前对象
    static CCompatibleType* instance();
    //读取数据兼容配置文件
    void readFile(const QString& filePath);
    //获取兼容性数据
    QMap<QString, QStringList> getCompatibleTypeData()
    {
        return m_data;
    }

private:
    CCompatibleType();
    CCompatibleType(const CCompatibleType&);
    CCompatibleType& operator=(const CCompatibleType&);
private:
    //当前单例对象
    static CCompatibleType* m_pInstance;
    //兼容数据
    QMap<QString, QStringList> m_data;
};

#endif // CCOMPATIBLETYPE_H
