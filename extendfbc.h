﻿#ifndef EXTENDFBC_H
#define EXTENDFBC_H

#include <QObject>
#include <QString>
#include <QDomDocument>
#include <QDomElement>
#include <QDomNode>
#include <QJsonObject>
#include <QJsonArray>
#include <QSharedPointer>
#include <QList>
#include <QFile>
#include <QTextStream>

//变量元素
class ExElement : public QObject
{
    Q_OBJECT
public:
    ExElement() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
signals:

public:
    //名称
    QString Name;
    //数组模式
    bool ListMode;
    //数据类型
    QString dataType;
};

//输入
class ExInput : public QObject
{
    Q_OBJECT
public:
    ExInput() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
signals:

public:
    //元素
    QList<QSharedPointer<ExElement>> elementList;
};

//输出
class ExOutput : public QObject
{
    Q_OBJECT
public:
    ExOutput() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
signals:

public:
    //元素
    QList<QSharedPointer<ExElement>> elementList;
};

//输入输出
class ExInOutput : public QObject
{
    Q_OBJECT
public:
    ExInOutput() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
signals:

public:
    //元素
    QList<QSharedPointer<ExElement>> elementList;
};

//本地
class ExLocal : public QObject
{
    Q_OBJECT
public:
    ExLocal() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
signals:

public:
    //元素
    QList<QSharedPointer<ExElement>> elementList;
};

//全局
class ExGlobal : public QObject
{
    Q_OBJECT
public:
    ExGlobal() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
signals:

public:
    //元素
    QList<QSharedPointer<ExElement>> elementList;
};


//变量
class Var : public QObject
{
    Q_OBJECT
public:
    Var() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
signals:

public:
    QSharedPointer<ExInput> input;
    QSharedPointer<ExOutput> output;
    QSharedPointer<ExInOutput> inout;
    QSharedPointer<ExLocal> local;
    QSharedPointer<ExGlobal> global;
};

//代码
class ExCode : public QObject
{
    Q_OBJECT
public:
    ExCode() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
signals:

public:
    //评估代码
    QString evalCode;
};

//元件
class ExComponent : public QObject
{
    Q_OBJECT
public:
    ExComponent() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
signals:

public:
    //显示名称
    QString Name;
    //是否启用
    bool Enable;
    //主类型FUNC
    QString Type;
    //子类型
    QString ChildType;
    //数据类型
    QString dataType;
    //变量
    QSharedPointer<Var> var;
    //代码
    QSharedPointer<ExCode> code;
};

//所有元件
class ExComponentList : public QObject
{
    Q_OBJECT
public:
    ExComponentList() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonArray toJsonArray();

signals:

public:
    QList<QSharedPointer<ExComponent>> componentList;
};

class ExtendFBC : public QObject
{
    Q_OBJECT
public:
    ExtendFBC() {};
    void toXml(QString path);
    void fromXml(QString path);
signals:

public:
    //版本
    QString Version;
    //更改时间
    QString LastChange;

    //文件路径
    QString filePath;

    //所有的元件
    QSharedPointer<ExComponentList> componentList = QSharedPointer<ExComponentList>(new ExComponentList());
};

#endif // EXTENDFBC_H
