﻿import QtQuick 2.15

//LD 或块
Rectangle {
    id: control

    //主控件
    property var mainControl
    //配置
    property var config: LDEditorConfig
    //线条起点的偏移量
    property int lineOffset: config.defaultLineHeightOffset + config.defaultConnectorHeight
                             + config.defaultConnectorMargin
    //绑定的数据
    property var bindData

    property int childwidth: 0

    property int vlineHeight: 0

    property color tailColor: config.defaultLineColor

    width: childwidth + config.defaultENENOWidth * 2

    color: "transparent"

    Repeater {
        model: bindData.Connectors
        //EN ENO引脚
        Rectangle {
            visible: model.Type === "EN" || model.Type === "ENO"
            width: config.defaultENENOWidth
            height: config.defaultLineWidth
            anchors {
                left: control.left
                leftMargin: model.Type === "EN" ? 0 : c_bl.width + config.defaultENENOWidth
                top: control.top
                topMargin: control.lineOffset
            }
            color: mainControl.isOnline ? (model.Value ? config.defaultLineTrueColor : config.defaultLineFalseColor) : config.defaultLineColor
            onColorChanged: {
                if (model.Type === "ENO") {
                    control.tailColor = color
                }
            }
            //引脚的标签名称
            Text {
                anchors {
                    horizontalCenter: parent.horizontalCenter
                    top: parent.top
                    topMargin: -config.fontPixelSize
                }
                visible: config.isShowLabelName
                font.pixelSize: config.fontPixelSize
                text: model.LabelName
            }
        }
    }

    Repeater {
        model: bindData.Connectors
        //纵向线
        Rectangle {
            visible: model.Type === "EN" || model.Type === "ENO"
            anchors {
                left: control.left
                leftMargin: model.Type === "EN" ? config.defaultENENOWidth : c_bl.width
                                                  + config.defaultENENOWidth
                top: control.top
                topMargin: control.lineOffset
            }
            width: config.defaultLineWidth
            height: control.vlineHeight
            color: mainControl.isOnline ? (model.Value ? config.defaultLineTrueColor : config.defaultLineFalseColor) : config.defaultLineColor
        }
    }

    Text {
        anchors {
            right: parent.right
            bottom: parent.bottom
        }
        font.pixelSize: config.fontPixelSize
        color: "lightgray"
        text: "OR"
    }

    //或块没有变量与注释
    Column {
        id: c_bl
        anchors {
            left: parent.left
            leftMargin: config.defaultENENOWidth
            top: parent.top
            topMargin: 0
        }
        Repeater {
            model: bindData.Blocks
            LDBlock {
                id: bl
                anchors {
                    centerIn: parent.Center
                }
                mainControl: control.mainControl
                bindData: model
                function reSize() {
                    var t = 0
                    var tmpwidth = 0
                    var tmpheight = 0
                    for (var i = 0; i < c_bl.children.length; i++) {
                        if (c_bl.children[i] instanceof LDBlock) {
                            if (t === 0) {
                                tmpwidth = c_bl.children[i].width
                                tmpheight = c_bl.children[i].height
                            } else {
                                if (tmpwidth < c_bl.children[i].width)
                                    tmpwidth = c_bl.children[i].width

                                if (i === c_bl.children.length - 2) {
                                    control.vlineHeight = tmpheight + config.defaultLineWidth
                                }
                                tmpheight += c_bl.children[i].height
                            }
                            t++
                        }
                    }
                    control.childwidth = tmpwidth
                    control.height = tmpheight
                }
                Component.onCompleted: {
                    reSize()
                }
                onHeightChanged: {
                    reSize()
                }
                onWidthChanged: {
                    reSize()
                }
                //尾线
                Rectangle {
                    id: tailLine
                    anchors {
                        left: bl.right
                        leftMargin: 0
                        top: bl.top
                        topMargin: control.lineOffset
                    }
                    width: control.childwidth - bl.width
                    height: config.defaultLineWidth
                    color: control.tailColor
                }
            }
        }
    }
}
