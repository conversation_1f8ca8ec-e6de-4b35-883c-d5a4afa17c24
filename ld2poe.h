// #ifndef LD2POE_H
// #define LD2POE_H
// #include <QSet>
// #include <QString>
// #include "saveld.h"

// class LD2POE
// {
// private:
//     QSet<QString> setConnectorName;

// public:
//     QString dfsFromLdblock(const QList<QSharedPointer<LDBlock>> &blocks, QString blockTypeFather, QString varOut);
//     QSet<QString> getNewVar()
//     {
//         return setConnectorName;
//     }
//     void clear()
//     {
//         setConnectorName.clear();
//     }
// };

// #endif // LD2POE_H
