﻿#ifndef LDCOORDINATEFIXER_H
#define LDCOORDINATEFIXER_H

#include <QObject>
#include <QJsonObject>
#include <QJsonArray>
#include <QSet>
#include <QMap>
#include <QPair>
#include <QSharedPointer>
#include <QStack>
#include <QList>
#include "ldobject.h"

class LDCoordinateFixer : public QObject
{
    Q_OBJECT
public:
    static LDCoordinateFixer& instance()
    {
        static LDCoordinateFixer instance;
        return instance;
    }

    // 修复元件坐标
    bool fixComponentCoordinates(LDFile* ld, int networkNumber);

private:
    // 获取start元件
    QSharedPointer<LDComponent> getStartComponent(LDFile* ld, int networkNumber);

    // 获取元件的最大输入路径长度
    int getMaxInputPathLength(LDFile* ld, int componentNumber, QSet<int>& visited);

    //第一行的最后一个元件
    QSharedPointer<LDComponent> lastComponentInFirstLine;

    bool preciseTraverseAndFix(LDFile* ld, int componentNumber,
        QStack<int>& traversalStack,
        QMap<int, QPair<int, int>>& componentCoordinates,
        QMap<QPair<int, int>, int>& occupiedPositions,
        QSet<int>& visited, QSet<int>& orBlocks,
        int networkNumber);

    // 处理特定的遍历模式
    bool handleSpecificTraversalPattern(LDFile* ld, int currentComponent,
        QStack<int>& traversalStack,
        QMap<int, QPair<int, int>>& componentCoordinates,
        QMap<QPair<int, int>, int>& occupiedPositions,
        QSet<int>& visited, QSet<int>& orBlocks,
        int networkNumber);

    // 寻找下一个可用的Y坐标
    int findNextAvailableY(int baseX, int startY,
        const QMap<QPair<int, int>, int>& occupiedPositions);

    // 分配分支坐标
    bool allocateBranchCoordinates(LDFile* ld, int sourceComponentNumber,
        const QList<int>& branchComponents,
        QMap<int, QPair<int, int>>& componentCoordinates,
        QMap<QPair<int, int>, int>& occupiedPositions,
        QSet<int>& orBlocks, int networkNumber);

    // 获取元件的所有连接信息
    struct ConnectionInfo
    {
        QList<int> horizontalTargets;    // 水平串联目标
        QList<int> branchTargets;        // 分支目标
    };
    // 获取元件的所有连接信息
    ConnectionInfo getComponentConnections(LDFile* ld, int componentNumber);

    // 应用坐标到元件
    void applyCoordinatesToComponents(LDFile* ld,
        const QMap<int, QPair<int, int>>& componentCoordinates);

    // 路径模拟相关函数
    // 模拟分支路径，预测坐标需求
    QList<int> simulateBranchPath(LDFile* ld, int startComponentNumber);

    // 递归模拟分支路径
    void simulateBranchPathRecursive(LDFile* ld, int componentNumber,
        QList<int>& pathComponents,
        QSet<int>& visited);

    // 模拟水平串联路径（只沿着SourceConnectIndex=-2, TargetConnectIndex=2的连接）
    QList<int> simulateHorizontalPath(LDFile* ld, int startComponentNumber);

    // 检查路径在指定Y坐标上是否有冲突
    bool checkPathConflicts(const QList<int>& pathComponents, int startX, int startY,
        const QMap<QPair<int, int>, int>& occupiedPositions,
        const QMap<int, QPair<int, int>>& componentCoordinates,
        LDFile* ld, QSet<int>& orBlocks);

    // 寻找能安全容纳整个路径的起始Y坐标
    int findSafeStartingY(int baseX, int preferredStartY, int pathLength,
        const QMap<QPair<int, int>, int>& occupiedPositions);

    // 获取元件的所有输出目标及其坐标
    struct OutputTarget
    {
        int componentNumber;
        int targetX;
        int targetY;
        int TargetConnectIndex;
    };
    QList<OutputTarget> getComponentOutputTargets(LDFile* ld, int componentNumber,
        const QMap<int, QPair<int, int>>& componentCoordinates);

    // 获取指定Y坐标上已占用位置的最大X坐标
    int getMaxXAtY(int targetY, const QMap<QPair<int, int>, int>& occupiedPositions);

    // 检查从源位置到目标位置的虚拟路径是否有冲突
    bool checkVirtualPathConflict(int sourceX, int sourceY, int targetX, int targetY,
        const QMap<QPair<int, int>, int>& occupiedPositions,
        int sourceComponent, int targetComponent);

    // 刷新任务顺序号
    void refreshTaskOrderNumber(LDFile* ld, int networkNumber, QSharedPointer<LDComponent> sourcecom);

    // 遍历从起始元件到目标元件的分支路径，按顺序分配TaskOrderNumber
    void traverseBranchPath(LDFile* ld, int startComponent, int endComponent, int networkNumber);

    // 获取OR块的所有输入分支元件
    QList<QSharedPointer<LDComponent>> getAllORBlockInputBranches(LDFile* ld, int orBlockNumber);

    // 设置网络中每个元件的父元件 每个元件的父元件为与它connectIndex = 0相连的输入元件
    void setParentComponent(LDFile* ld, int networkNumber);

private:
    //计算任务执行顺序的OR元件栈
    QStack<QSharedPointer<LDComponent>> tmpComStack;
    //计算任务执行顺序的执行号
    int taskOrderIndex;
    //访问状态管理，防止重复遍历
    QSet<int> visitedComponents;
};
#endif // LDCOORDINATEFIXER_H
