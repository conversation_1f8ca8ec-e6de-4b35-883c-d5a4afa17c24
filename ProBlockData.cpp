﻿// #include "ProBlockData.h"
// #include <QFile>
// #include <QTextStream>
// #include <QCoreApplication>
// #include <QDir>
// #include <QDomElement>
// #include <QDomDocument>

// QMap<QString, QMap<QString, XMLFuncition>>ProBlockData::m_mapFuncBlockListData = initProFuncBlockListData();
// QString ProBlockData::m_projectDirectory = QString();
// QString ProBlockData::m_projectPath = QString();

// ProBlockData::ProBlockData(QObject* parent)
//     : QObject{ parent }
// {

// }

// void ProBlockData::setProjectPathAndDirectory(QString dir, QString path)
// {
//     m_projectDirectory = dir;
//     m_projectPath = path;
// }

// void ProBlockData::reflushProFuncBlockListData()
// {
//     QMap<QString, QMap<QString, XMLFuncition>>mapProBlockData = readProBlockData();
//     QMap<QString, XMLFuncition>mapPttData = readPttFunctionBlockData();

//     m_mapFuncBlockListData = mapProBlockData;
//     m_mapFuncBlockListData.insert("ptt", mapPttData);

//     QString varqPath = m_projectPath;
//     QString coreName = getActivateCore(varqPath);
//     QString optimaziton = getOptimization(varqPath, coreName);

//     if (optimaziton != "3") // not C code
//     {
//         QMap<QString, XMLFuncition>mapFirmwareData = readFirmwareData();
//         m_mapFuncBlockListData.insert("firmware", mapFirmwareData);
//     }
// }

// QMap<QString, QMap<QString, XMLFuncition> >& ProBlockData::getAllFuncBlockListData()
// {
//     return m_mapFuncBlockListData;
// }


// QStringList ProBlockData::readTextData(QString path)
// {
//     QString lineStr;
//     QStringList listData;
//     QFile file(path);
//     bool isInComment = false;
//     if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
//     {
//         return listData;
//     }
//     QTextStream txtInput(&file);
//     while (!txtInput.atEnd())
//     {
//         lineStr = txtInput.readLine();

//         if (lineStr.trimmed().size() != 0)
//         {
//             if (!isInComment && lineStr.contains("(*") && lineStr.contains("*)"))
//             {
//                 isInComment = false;
//             }
//             else if (!isInComment && lineStr.contains("(*"))
//             {
//                 isInComment = true;
//                 continue;
//             }
//             else if (isInComment && lineStr.contains("*)"))
//             {
//                 isInComment = false;
//                 continue;
//             }

//             if (!isInComment)
//             {
//                 listData.push_back(lineStr);
//             }
//         }
//     }

//     file.close();
//     return listData;
// }

// QMap<QString, QMap<QString, XMLFuncition> > ProBlockData::initProFuncBlockListData()
// {
//     QMap<QString, QMap<QString, XMLFuncition>>map;
//     map.clear();
//     return map;
// }

// QMap<QString, XMLFuncition> ProBlockData::readFirmwareData()
// {
//     QString path = QCoreApplication::applicationDirPath();
//     QString functionpath = path + "/Function/IEC_STANDARD_FUN_CFCFBD_BL.inc";
//     QMap<QString, XMLFuncition> firmwareData = readFunction(functionpath);
//     return firmwareData;
// }

// QMap<QString, XMLFuncition> ProBlockData::readPttFunctionBlockData()
// {
//     QString pttdir = m_projectDirectory + "/$GEN$/";
//     QMap<QString, XMLFuncition> pttfunctions = readPttFunctionAndBlock(pttdir);

//     for (int pttIndex = 0; pttIndex < pttfunctions.keys().size(); pttIndex++)
//     {
//         pttfunctions[pttfunctions.keys().at(pttIndex)].mode = readFuncBlockMode(pttfunctions.keys().at(pttIndex) + ".CFC");
//     }

//     return pttfunctions;
// }

// QString ProBlockData::getActivateCore(QString proPath)
// {
//     QFile xmlFile(proPath);
//     if (!xmlFile.open(QFile::ReadOnly))
//     {
//         return "";
//     }
//     QDomDocument docXML;
//     if (!docXML.setContent(&xmlFile))
//     {
//         xmlFile.close();
//         return "";
//     }
//     xmlFile.close();

//     QDomElement xmlRoot = docXML.documentElement(); // 返回根节点
//     QDomNode domNode = xmlRoot.firstChild();
//     while (!domNode.isNull())
//     {
//         if (domNode.isElement())
//         {
//             QDomElement domElement = domNode.toElement();
//             if (!domElement.isNull())
//             {
//                 if (xmlRoot.hasAttribute("Active"))
//                 {
//                     QString activecorename = xmlRoot.attribute("Active");
//                     //MainPlatform::getDefault()->setCurActiveCoreName(activecorename);
//                     return activecorename;
//                 }
//             }
//         }
//         domNode = domNode.nextSibling();
//     }

//     return "";
// }

// QMap<QString, XMLFuncition> ProBlockData::readFunctionBlockData()
// {
//     QString varqPath = m_projectPath;
//     QString coreName = getActivateCore(varqPath);
//     QString incPath = getIncPath(varqPath, coreName);
//     QMap<QString, XMLFuncition> blockData = readFunctionBlock(incPath);

//     QString folder = incPath.split("/" + incPath.split("/").last()).first().split("/").last();
//     QString iniPath = incPath.split(incPath.split("/").last()).first() + folder + ".INI";

//     QString optizimation = getOptimization(varqPath, coreName);
//     QString ProjectDir = varqPath.left(varqPath.lastIndexOf("/"));//获取工程路径
//     if (optizimation == "3") // C code
//     {
//         QString xmlFilepath = incPath.left(incPath.lastIndexOf("/")) + "/" + coreName + ".XML";
//         iniPath = ProjectDir + "/$ENV$/" + coreName + "/" + getHardwareName(xmlFilepath) + ".ini";
//     }
//     //根据ini获取mode
//     checkModuleMode(iniPath, blockData);


//     return blockData;
// }



// QMap<QString, QMap<QString, XMLFuncition> > ProBlockData::readProBlockData()
// {
//     QMap<QString, QMap<QString, XMLFuncition>>mapProBlockData;

//     QStringList proAllCores = getProAllCore();
//     QString varqPath = m_projectPath;

// #undef USE_ALL_FB //加载所有当前工程中的所有资源下的FB
// #ifdef USE_ALL_FB
//     QMap<QString, QString> proAllCoreNameType = getProAllCoreAndType();
//     for (int codeIndex = 0; codeIndex < proAllCores.size(); codeIndex++)
//     {
//         QString coreName = proAllCores.at(codeIndex)
//             QString coreType = proAllCoreNameType.value(coreName);
//         if (mapProBlockData.contains(coreType))
//         {
//             continue;
//         }
//         QString ProjectDir = varqPath.left(varqPath.lastIndexOf("/"));//获取工程路径
//         QString incPath = getIncPath(varqPath, coreName);

//         QString optimization = getOptimization(varqPath, proAllCores.at(codeIndex));
//         if (optimization == "3") //C code,reset INC path
//         {
//             incPath = ProjectDir + "/$ENV$/" + proAllCores.at(codeIndex) + "/" + "GLOBPROT.INC";
//         }
//         QMap<QString, XMLFuncition> blockData = readFunctionBlock(incPath);

//         QString folder = incPath.split("/" + incPath.split("/").last()).first().split("/").last();
//         QString iniPath = incPath.split(incPath.split("/").last()).first() + folder + ".INI";


//         if (optimization == "3") //C code,reset INC path
//         {
//             QString xmlFilepath = incPath.left(incPath.lastIndexOf("/")) + "/" + proAllCores.at(codeIndex) + ".XML";
//             iniPath = ProjectDir + "/$ENV$/" + proAllCores.at(codeIndex) + "/" + getHardwareName(xmlFilepath) + ".ini";
//         }
//         //根据ini获取mode
//         checkModuleMode(iniPath, blockData);
//         mapProBlockData.insert(coreType, blockData);
//     }
// #else
//     QString coreName = getActivateCore(varqPath);
//     QString ProjectDir = varqPath.left(varqPath.lastIndexOf("/"));//获取工程路径
//     QString incPath = getIncPath(varqPath, coreName);

//     QString optimization = getOptimization(varqPath, coreName);
//     if (optimization == "3") //C code,reset INC path
//     {
//         incPath = ProjectDir + "/$ENV$/" + coreName + "/" + "GLOBPROT.INC";
//     }
//     QMap<QString, XMLFuncition> blockData = readFunctionBlock(incPath);

//     QString folder = incPath.split("/" + incPath.split("/").last()).first().split("/").last();
//     QString iniPath = incPath.split(incPath.split("/").last()).first() + folder + ".INI";


//     if (optimization == "3") //C code,reset INC path
//     {
//         QString xmlFilepath = incPath.left(incPath.lastIndexOf("/")) + "/" + coreName + ".XML";
//         iniPath = ProjectDir + "/$ENV$/" + coreName + "/" + getHardwareName(xmlFilepath) + ".ini";
//     }
//     //根据ini获取mode
//     checkModuleMode(iniPath, blockData);
//     mapProBlockData.insert(coreName, blockData);
// #endif

//     return mapProBlockData;
// }

// QStringList ProBlockData::readPttTextData(QString dirpath)
// {
//     QString lineStr;
//     QStringList listData;

//     QString tmppath;
//     QDir dir(dirpath);
//     dir.setFilter(QDir::Files | QDir::NoSymLinks);
//     QFileInfoList list = dir.entryInfoList();

//     foreach(QFileInfo mfi, list)
//     {
//         if (mfi.isFile())
//         {
//             if (mfi.suffix().toLower() == "ptt")
//             {

//                 tmppath = mfi.absoluteFilePath();
//                 QFile file(tmppath);
//                 if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
//                 {
//                     return listData;
//                 }
//                 QTextStream txtInput(&file);
//                 while (!txtInput.atEnd())
//                 {
//                     lineStr = txtInput.readLine();
//                     lineStr = lineStr.trimmed();
//                     if (lineStr.isEmpty())
//                     {
//                         continue;
//                     }
//                     listData.push_back(lineStr);
//                 }
//                 file.close();
//             }
//         }

//     }



//     return listData;
// }

// QStringList ProBlockData::getProAllCore()
// {
//     //  QString pttdir = MainPlatform::getDefault()->getProjectPath();

//     QFile xmlFile(m_projectPath);
//     if (!xmlFile.open(QFile::ReadOnly))
//     {
//         return QStringList();
//     }
//     QDomDocument docXML;
//     if (!docXML.setContent(&xmlFile))
//     {
//         xmlFile.close();
//         return QStringList();
//     }
//     xmlFile.close();


//     QStringList coreNames;
//     QDomElement xmlRoot = docXML.documentElement(); // 返回根节点

//     QDomElement cpuElement;
//     cpuElement = xmlRoot.firstChildElement("CPU");
//     QDomElement coreElement;

//     while (!cpuElement.isNull() && cpuElement.isElement())
//     {
//         QDomNodeList  coreNodeList = cpuElement.childNodes();
//         for (int coreIndex = 0; coreIndex < coreNodeList.size(); coreIndex++)
//         {
//             coreElement = coreNodeList.at(coreIndex).toElement();
//             if (!coreElement.isNull() || coreElement.isElement())
//             {
//                 coreNames << coreElement.attribute("Name");
//             }
//         }
//         cpuElement = cpuElement.nextSiblingElement("CPU");
//     }


//     return coreNames;
// }

// QMap<QString, QString> ProBlockData::getProAllCoreAndType()
// {
//     //  QString pttdir = MainPlatform::getDefault()->getProjectPath();

//     QFile xmlFile(m_projectPath);
//     if (!xmlFile.open(QFile::ReadOnly))
//     {
//         return QMap<QString, QString>();
//     }
//     QDomDocument docXML;
//     if (!docXML.setContent(&xmlFile))
//     {
//         xmlFile.close();
//         return QMap<QString, QString>();
//     }
//     xmlFile.close();


//     QMap<QString, QString> coreNames;
//     QDomElement xmlRoot = docXML.documentElement(); // 返回根节点

//     QDomElement cpuElement;
//     cpuElement = xmlRoot.firstChildElement("CPU");
//     QDomElement coreElement;

//     while (!cpuElement.isNull() && cpuElement.isElement())
//     {
//         QString coreName = QString("");
//         QString coreType = QString("");
//         QDomNodeList  coreNodeList = cpuElement.childNodes();
//         for (int coreIndex = 0; coreIndex < coreNodeList.size(); coreIndex++)
//         {
//             coreElement = coreNodeList.at(coreIndex).toElement();
//             if (!coreElement.isNull() || coreElement.isElement())
//             {
//                 coreName = coreElement.attribute("Name");
//                 coreType = coreElement.attribute("Type");
//                 coreNames.insert(coreName, coreType);
//             }
//         }
//         cpuElement = cpuElement.nextSiblingElement("CPU");
//     }


//     return coreNames;
// }

// QString ProBlockData::getIncPath(QString proPath, QString coreName)
// {
//     QString newPath = QString("");
//     QString projectDir = proPath.left(proPath.lastIndexOf("/"));// get VARQ path
//     //MaxSpeed:0,BL code;Speed:1,Native;Size:2,U code;Firmware:3,C code
//     QString optimization = getOptimization(projectDir, coreName);
//     if (optimization == "3") //C Code
//     {
//         newPath = projectDir + "/$ENV$/" + coreName + "/" + "GLOBPROT.INC";
//     }
//     else //BL Code,U code and Native code
//     {
//         QString indexName = getCoreType(proPath, coreName);

//         QMap<QString, QString>mapModuleTypeFile = getModulesType();
//         indexName = mapModuleTypeFile.value(indexName);
//         QString projectPath = QCoreApplication::applicationDirPath();

//         QString incPath = projectPath + "/Modules/" + indexName + "/" + "GLOBPROT.INC";
//         //newPath =  incPath.replace(" ","");
//         newPath = incPath;
//     }
//     return newPath;
// }

// QString ProBlockData::getOptimization(QString proPath, QString coreName)
// {
//     QString proDir = QString("");
//     if (proPath.toUpper().lastIndexOf(".VARQ") > 0)
//     {
//         proDir = proPath.left(proPath.lastIndexOf("/"));
//     }
//     else
//     {
//         proDir = proPath;
//     }
//     QString xmlName = proDir + "/$ENV$/" + coreName + "/" + coreName + ".XML";

//     QDomDocument PropFileDoc;
//     QFile PropFile(xmlName);
//     if (!PropFile.open(QIODevice::ReadOnly))
//     {
//         //QMessageBox::warning(nullptr, SOFTWARE_TITLE, QString(tr("Read active core properties file faile")));
//     }
//     PropFileDoc.setContent(&PropFile);

//     //
//     QDomElement rootElement = PropFileDoc.documentElement();
//     //QString HardwareModule = rootElement.attribute("HardwareModule");

//     QDomNodeList nodeList = PropFileDoc.elementsByTagName("Optimization");
//     if (nodeList.count() == 0)
//     {
//         //QMessageBox::warning(nullptr, SOFTWARE_TITLE, QString(tr("active core properties file error")));
//     }

//     QDomElement OptimizationElement = nodeList.at(0).toElement();//一定会存在
//     QString optimization = OptimizationElement.text();
//     return optimization;
// }

// QString ProBlockData::getCoreType(QString proPath, QString coreName)
// {
//     QString coreType = "";
//     QFile xmlFile(proPath);
//     if (!xmlFile.open(QFile::ReadOnly | QFile::Text))
//     {
//         return coreType;
//     }
//     QDomDocument docXML;
//     if (!docXML.setContent(&xmlFile))
//     {
//         xmlFile.close();
//         return  coreType;
//     }
//     xmlFile.close();

//     QDomElement xmlRoot = docXML.documentElement(); // 返回根节点
//     QDomNode domNode = xmlRoot.firstChild();
//     QString active = xmlRoot.attribute("Active");
//     while (!domNode.isNull())
//     {
//         if (domNode.isElement())
//         {
//             QDomElement domElement = domNode.toElement();
//             if (!domElement.isNull())
//             {
//                 if (domElement.nodeName() == "CPU")
//                 {
//                     QDomElement cpuDom = domElement.toElement();
//                     QDomNodeList list = domElement.childNodes();
//                     for (int i = 0; i < list.count(); i++)
//                     {
//                         QDomNode node = list.at(i);
//                         QDomElement nodeData = node.toElement();
//                         if (node.nodeName() == "Core" && nodeData.attribute("Name") == coreName)
//                         {
//                             coreType = nodeData.attribute("Type").trimmed();
//                             return coreType;
//                         }
//                     }
//                 }
//             }
//         }
//         domNode = domNode.nextSibling();
//     }
//     return coreType;
// }

// QMap<QString, QString> ProBlockData::getModulesType()
// {
//     //获取modules的所有类型注意在xml里去找类型

//     QString tempPath = QCoreApplication::applicationDirPath();
//     QString modulespath = tempPath + "/Modules";

//     QDir fileDir(modulespath);

//     QStringList list = fileDir.entryList(QStringList() << "*.XML");

//     XMLRes resourcesFile;
//     QStringList resourcsFileName;
//     for (int modulesIndex = 0; modulesIndex < list.size(); modulesIndex++)
//     {
//         resourcesFile = readSourcesXML(modulespath + "/" + list.at(modulesIndex));
//         if (resourcesFile.FileType == QString("Module"))
//         {
//             //缓存modules 名
//             resourcsFileName << resourcesFile.LDFile;
//         }
//     }

//     //
//     QMap<QString, QString>mapFileType;
//     for (int fileIndex = 0; fileIndex < resourcsFileName.size(); fileIndex++)
//     {
//         QString hardwarePath = modulespath + "/" + resourcsFileName.at(fileIndex) + "/" + resourcsFileName.at(fileIndex) + ".XML";
//         QString  hardwareName = getHardwareName(hardwarePath);
//         if (hardwareName.trimmed().size() != 0)
//         {
//             mapFileType.insert(hardwareName, resourcsFileName.at(fileIndex));

//         }
//     }

//     return mapFileType;
// }

// XMLRes ProBlockData::readSourcesXML(QString configName)
// {
//     XMLRes data;
//     QFile xmlFile(configName);
//     if (!xmlFile.open(QFile::ReadOnly))
//     {
//         return data;
//     }
//     QDomDocument docXML;
//     if (!docXML.setContent(&xmlFile))
//     {
//         xmlFile.close();
//         return data;
//     }
//     xmlFile.close();

//     QDomElement xmlRoot = docXML.documentElement(); // 返回根节点
//     QDomNode domNode = xmlRoot.firstChild();
//     while (!domNode.isNull())
//     {
//         if (domNode.isElement())
//         {
//             QDomElement domElement = domNode.toElement();
//             if (domElement.tagName() == "Description")
//             {
//                 data.Descirption = domElement.text();
//             }
//             else if (domElement.tagName() == "FileType")
//             {
//                 data.FileType = domElement.text();
//             }
//             else if (domElement.tagName() == "Icon")
//             {
//                 data.Icon = domElement.text();
//             }
//             else if (domElement.tagName() == "caption")
//             {
//                 data.Caption = domElement.text();
//             }
//             else if (domElement.tagName() == "LDFile")
//             {
//                 data.LDFile = domElement.text();
//             }
//         }
//         domNode = domNode.nextSibling();
//     }
//     return  data;
// }

// QString ProBlockData::getHardwareName(QString path)
// {
//     //获取硬件配置xml里面的硬件类型

//     QFile xmlFile(path);

//     //硬件名字
//     QString hardwareName;
//     hardwareName.clear();
//     if (!xmlFile.exists())
//     {
//         return hardwareName;
//     }



//     if (!xmlFile.open(QFile::ReadOnly))
//     {
//         return hardwareName;
//     }
//     QDomDocument docXML;
//     if (!docXML.setContent(&xmlFile))
//     {
//         xmlFile.close();
//         return hardwareName;
//     }
//     xmlFile.close();

//     QDomElement xmlRoot = docXML.documentElement(); // 返回根节点

//     if (!xmlRoot.isNull())
//     {
//         return xmlRoot.attribute("HardwareModule");
//     }
//     return hardwareName;
// }

// QStringList ProBlockData::returnFunctionDatas(QString datas, bool type)
// {
//     QStringList returnData;
//     int lenght = datas.count();
//     if (lenght == 0)
//     {
//         returnData.push_back("");
//         return  returnData;
//     }
//     QString str = datas;
//     str.remove(QRegExp("\\s"));
//     str.remove(QRegExp("\\t"));
//     int typePositon = datas.indexOf(":");

//     if (type)
//     {
//         int position = datas.indexOf("FUNCTION");
//         int nameLenght = typePositon - position;
//         int typeLenght = lenght - typePositon;

//         QString nameData = datas.mid(position + 9, nameLenght - 9).trimmed();
//         QString typeData = datas.mid(typePositon + 2, typeLenght - 2).trimmed(); //存在空格
//         returnData.push_back(nameData);
//         returnData.push_back(typeData);
//     }
//     else
//     {
//         int typeLenght = lenght - typePositon;
//         int namePosition = datas.indexOf("I");
//         int nameLenght = typePositon - namePosition;
//         QString varName = datas.mid(namePosition, nameLenght - 1).trimmed();
//         QString varType = datas.mid(typePositon + 2, typeLenght - 2).trimmed();
//         returnData.push_back(varName);
//         returnData.push_back(varType);
//     }
//     return returnData;
// }

// void ProBlockData::checkModuleMode(QString iniPath, QMap<QString, XMLFuncition>& blockData)
// {
//     QStringList blockNames = blockData.keys();

//     //根据block的名字获取键值对
//     QMap<QString, QString>mapBlockKeyValue = checkKeyValue(blockNames, iniPath);

//     //key:BlockKeyValue的value value:mode的类型 :ISN
//     QMap<QString, QString>mapValueMode = checkModeData(mapBlockKeyValue, iniPath);

//     for (int blockIndex = 0; blockIndex < blockData.keys().size(); blockIndex++)
//     {

//         //找不到键值对默认没有
//         if (mapBlockKeyValue.value(blockData.keys().at(blockIndex)).isEmpty()
//             || mapBlockKeyValue.value(blockData.keys().at(blockIndex)).isNull())
//         {
//             blockData[blockData.keys().at(blockIndex)].mode = "N";
//         }
//         else
//         {
//             QString mode = mapValueMode.value(mapBlockKeyValue.value(blockData.keys().at(blockIndex)));
//             blockData[blockData.keys().at(blockIndex)].mode = mode;
//         }


//     }
// }

// QMap<QString, QString> ProBlockData::checkKeyValue(QStringList blobckNames, QString iniPath)
// {
//     // qDebug()<<__func__<<":"<<iniPath;
//     QFile file(iniPath);

//     QMap<QString, QString> mapBlockKeyValue;
//     mapBlockKeyValue.clear();
//     if (!file.exists())
//     {
//         return mapBlockKeyValue;
//     }
//     if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
//     {
//         return mapBlockKeyValue;
//     }

//     QTextStream in(&file);
//     bool bFlag = false;
//     QString key;
//     QString value;
//     while (!in.atEnd())
//     {
//         QString line = in.readLine();
//         if (bFlag)
//         {
//             if (line.trimmed().size() == 0)
//             {
//                 continue;
//             }


//             if (line.at(0) == "[" && line.at(line.size() - 1) == "]")
//             {
//                 break;
//             }

//             line.remove(QRegExp("\\s"));
//             if (blobckNames.contains(line.split("=").first()))
//             {
//                 key = blobckNames.at(blobckNames.indexOf(line.split("=").first()));
//                 value = line.trimmed().split("=").last();
//                 mapBlockKeyValue.insert(key, value);
//             }
//         }
//         if (line == QString("[Firmware]"))
//         {
//             bFlag = true;
//         }
//         //process_line(line);
//     }
//     return mapBlockKeyValue;

// }

// QMap<QString, QString> ProBlockData::checkModeData(QMap<QString, QString> blockKeyValue, QString iniPath)
// {
//     //根据键值对的值获取mode的类型

//     QMap<QString, QString> mapBlockKeyValue;
//     bool initMode;
//     bool systemMode;
//     bool normalMode;

//     QFile file(iniPath);
//     mapBlockKeyValue.clear();
//     if (!file.exists())
//     {
//         return mapBlockKeyValue;
//     }
//     if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
//     {
//         return mapBlockKeyValue;
//     }

//     QTextStream in(&file);
//     QStringList blockValue = blockKeyValue.values();
//     QString group;
//     bool bFlag = false;
//     QString line;
//     while (!in.atEnd())
//     {
//         if (!bFlag)
//         {
//             line = in.readLine();
//         }
//         if (line.trimmed().size() > 0)
//         {
//             if (line.at(0) == "[" && line.at(line.size() - 1) == "]")
//             {
//                 line.remove(0, 1);
//                 group = line.remove(line.size() - 1, 1);
//                 if (blockValue.contains(group))
//                 {
//                     normalMode = 0;
//                     systemMode = 0;
//                     initMode = 0;
//                     while (!in.atEnd())
//                     {
//                         line = in.readLine();

//                         if (line.size() > 0)
//                         {
//                             if (line.at(0) != "[" && line.at(line.size() - 1) != "]")
//                             {
//                                 if (line.split("=").first() == QString("InitMode"))
//                                 {
//                                     initMode = line.split("=").last().toInt();
//                                 }
//                                 else if (line.split("=").first() == QString("SystemMode"))
//                                 {
//                                     systemMode = line.split("=").last().toInt();
//                                 }
//                                 else if (line.split("=").first() == QString("NormalMode"))
//                                 {
//                                     normalMode = line.split("=").last().toInt();
//                                 }
//                             }
//                             else if (line.at(0) == "[" && line.at(line.size() - 1) == "]")
//                             {
//                                 mapBlockKeyValue.insert(group, judgeISN(initMode, systemMode, normalMode));
//                                 bFlag = true;
//                                 break;
//                             }
//                         }

//                     }

//                     mapBlockKeyValue.insert(group, judgeISN(initMode, systemMode, normalMode));
//                     bFlag = true;


//                 }
//                 else
//                 {
//                     bFlag = false;

//                 }
//             }
//         }
//     }

//     return mapBlockKeyValue;
// }

// QString ProBlockData::judgeISN(bool bInitMode, bool bSystemMode, bool bNormalMode)
// {
//     //根据isn 判断mode
//     QString mode;
//     mode.clear();

//     if (bInitMode)
//     {
//         mode.append("I");
//     }

//     if (bSystemMode)
//     {
//         mode.append("S");

//     }

//     if (bNormalMode)
//     {
//         mode.append("N");
//     }

//     //所有mode找不到就是N
//     if (mode.size() == 0)
//     {
//         mode.append("N");
//     }
//     return mode;
// }

// QString ProBlockData::readFuncBlockMode(QString cfcFileName)
// {
//     //在功能块的cfc里面读取mode
//     QString projectDir = m_projectDirectory;
//     QString cfcFilePath = projectDir + "/" + "UserFBs/" + cfcFileName;

//     QFile file(cfcFilePath);

//     if (!file.exists())
//     {
//         return QString("N");
//     }

//     if (!file.open(QIODevice::ReadOnly))
//     {
//         return QString("N");
//     }

//     QDomDocument docXML;
//     if (!docXML.setContent(&file))
//     {
//         file.close();
//         return QString("N");
//     }
//     file.close();


//     QDomElement xmlRoot = docXML.documentElement(); // 返回根节点

//     if (!xmlRoot.isNull())
//     {
//         return xmlRoot.attribute("model");
//     }
//     return QString("N");

// }

// QMap<QString, XMLFuncition> ProBlockData::readFunction(QString path)
// {
//     bool functionAdd = false;
//     bool varAdd = false;

//     QStringList datas = readTextData(path);
//     QMap<QString, XMLFuncition>firmwareData;
//     if (datas.count() < 2)
//     {
//         return firmwareData;
//     }
//     for (int i = 0; i < datas.count(); i++)
//     {
//         XMLFuncition functionData;
//         //默认赋值为N
//         functionData.mode = QString("N");
//         int nums = 0;
//         functionAdd = false;
//         varAdd = false;
//         if (datas[i].contains(QRegExp("GLOBAL_PROTOTYP_BEGIN")))
//         {
//             if (datas[i + 1].contains(QRegExp("FUNCTION")))
//             {
//                 int output = 0;
//                 QStringList functionDatas = returnFunctionDatas(datas[i + 1], 1);
//                 if (functionDatas.count() <= 1)
//                 {
//                     return firmwareData;
//                 }
//                 functionData.funcitionType = functionData.FUNCITION;
//                 functionData.type = functionDatas[0].trimmed();
//                 functionData.outType = functionDatas[1].trimmed();
//                 functionAdd = true;

//                 if (datas[i + 2].contains(QRegExp("VAR_INPUT")))
//                 {
//                     int index = i + 2;
//                     for (int a = index + 1; a < datas.count(); a++) //读取有多少个输入
//                     {
//                         if (datas[a].contains(QRegExp("END_VAR")))
//                         {
//                             output = index;
//                             break;
//                         }
//                         index++;
//                     }
//                     for (int j = 2 + i; j < index; ++j) //读取输入
//                     {
//                         nums++;
//                         QStringList intput = returnFunctionDatas(datas[j + 1], 0);

//                         XMLIOPut itemData;
//                         itemData.name = intput.at(0);
//                         itemData.type = intput.at(1).trimmed().toUpper();
//                         //处理特殊类型
//                         if (itemData.type.contains("BOOL"))
//                         {
//                             itemData.type = "BOOL";
//                         }
//                         if (itemData.type.contains(";"))
//                         {
//                             itemData.type.remove(";");
//                         }
//                         itemData.index = nums;
//                         functionData.inPut.push_back(itemData);
//                     }

//                     varAdd = true;
//                 }
//                 if (functionAdd == true && varAdd == true)
//                 {
//                     firmwareData.insert(functionData.type, functionData);
//                 }
//             }
//         }
//     }
//     return firmwareData;
// }

// QMap<QString, XMLFuncition> ProBlockData::readPttFunctionAndBlock(QString path)
// {
//     bool functionAdd = false;
//     bool varAdd = false;
//     QStringList datas = readPttTextData(path);
//     QMap<QString, XMLFuncition>firmwareData;
//     if (datas.count() < 2)
//     {
//         return firmwareData;
//     }
//     for (int i = 0; i < datas.count() - 1; i++)
//     {
//         bool   outAdd = false;
//         bool    inAdd = false;
//         if ((datas[i].contains(QRegExp("FUNCTION_BLOCK"))))
//         {
//             XMLFuncition functionBlockData;
//             int inNums = 0;
//             int outNums = 0;
//             functionAdd = false;
//             outAdd = false;
//             inAdd = false;


//             if (datas[i].contains(QRegExp("END_FUNCTION_BLOCK")))
//             {
//                 continue;
//             }
//             {
//                 if (datas[i].contains(QRegExp("FUNCTION_BLOCK", Qt::CaseSensitive, QRegExp::FixedString)))
//                 {
//                     int output = 0;
//                     QStringList block = returnFunctionBlock(datas[i], 1); //Qstringlist中只有一位
//                     if (block.at(0) == "")
//                     {
//                         return firmwareData;
//                     }
//                     functionBlockData.funcitionType = functionBlockData.FUNCITION_BLOCK;
//                     functionBlockData.type = block.at(0).trimmed();
//                     functionAdd = true;
//                     if (datas[i + 1].contains(QRegExp("VAR_INPUT"))) //读取输入
//                     {
//                         int index = 2 + i;
//                         for (int a = index; a < datas.count(); a++)
//                         {
//                             if (datas[a].contains(QRegExp("END_VAR")))
//                             {
//                                 output = index;
//                                 break;
//                             }
//                             index++;
//                         }                                //此处可能存在index=2
//                         for (int j = 2 + i; j < index; ++j) //读取输入  Qstringlist中有三位，一：名称 二：类型 三：默认值
//                         {
//                             inNums++;
//                             QStringList intputlist = returnFunctionBlock(datas[j], 0);
//                             XMLIOPut itemData;
//                             itemData.name = intputlist.at(0);
//                             itemData.type = intputlist.at(1).trimmed().toUpper();

//                             //处理特殊类型
//                             if (itemData.type.contains("BOOL"))
//                             {
//                                 itemData.type = "BOOL";
//                             }

//                             itemData.initValue = intputlist.at(2);
//                             itemData.defaultValue = intputlist.at(3);
//                             itemData.index = inNums;
//                             functionBlockData.inPut.push_back(itemData);
//                         }
//                         inAdd = true;
//                     }
//                     if (output > 0 && datas[output + 1].contains(QRegExp("VAR_OUTPUT"))) //读取输出
//                     {
//                         int outIndex = output + 1 + 1;
//                         for (int b = outIndex; b < datas.count(); b++)
//                         {
//                             if (datas[b].contains(QRegExp("END_VAR")))
//                             {
//                                 break;
//                             }
//                             outIndex++;
//                         }
//                         for (int outI = output + 1 + 1; outI < outIndex;
//                             outI++) //读取输出 Qstringlist中有三位，一：名称 二：类型 三：默认值
//                         {
//                             outNums++;
//                             QStringList outputlist = returnFunctionBlock(datas[outI], 0);
//                             XMLIOPut itemData;
//                             itemData.name = outputlist.at(0);
//                             itemData.type = outputlist.at(1).trimmed().toUpper();
//                             //处理特殊类型
//                             if (itemData.type.contains("BOOL"))
//                             {
//                                 itemData.type = "BOOL";
//                             }
//                             itemData.index = outNums;
//                             functionBlockData.outPut.push_back(itemData);
//                         }
//                         outAdd = true;
//                     }
//                 }
//                 if (functionAdd == true && outAdd == true && inAdd == true)
//                 {
//                     firmwareData.insert(functionBlockData.type, functionBlockData);
//                 }
//             }
//         }
//         else
//         {
//             XMLFuncition functionData;
//             int nums = 0;
//             functionAdd = false;
//             varAdd = false;
//             if (datas[i].contains(QRegExp("FUNCTION")))
//             {
//                 if (datas[i].contains(QRegExp("END_FUNCTION")))
//                 {
//                     continue;
//                 }
//                 int output = 0;
//                 QStringList functionDatas = returnFunctionDatas(datas[i], 1);
//                 if (functionDatas.count() <= 1)
//                 {
//                     return firmwareData;
//                 }
//                 functionData.funcitionType = functionData.FUNCITION;
//                 functionData.type = functionDatas[0].trimmed();
//                 functionData.outType = functionDatas[1].trimmed();
//                 functionAdd = true;

//                 if (datas[i + 1].contains(QRegExp("VAR_INPUT")))
//                 {
//                     int index = i + 1;
//                     for (int a = index + 1; a < datas.count(); a++) //读取有多少个输入
//                     {
//                         if (datas[a].contains(QRegExp("END_VAR")))
//                         {
//                             output = index;
//                             break;
//                         }
//                         index++;
//                     }
//                     for (int j = 1 + i; j < index; ++j) //读取输入
//                     {
//                         nums++;
//                         QStringList intput = returnFunctionDatas(datas[j + 1], 0);

//                         XMLIOPut itemData;
//                         itemData.name = intput.at(0);
//                         itemData.type = intput.at(1).trimmed().toUpper();
//                         //处理特殊类型
//                         if (itemData.type.contains("BOOL"))
//                         {
//                             itemData.type = "BOOL";
//                         }
//                         if (itemData.type.contains(";"))
//                         {
//                             itemData.type.remove(";");
//                         }
//                         itemData.index = nums;
//                         functionData.inPut.push_back(itemData);
//                     }

//                     varAdd = true;
//                 }
//                 if (functionAdd == true && varAdd == true)
//                 {
//                     firmwareData.insert(functionData.type, functionData);
//                 }
//             }
//         }
//     }
//     return firmwareData;
// }

// QMap<QString, XMLFuncition> ProBlockData::readFunctionBlock(QString path)
// {
//     bool functionAdd = false;
//     bool outAdd = false;
//     bool inAdd = false;

//     QMap<QString, XMLFuncition>blockData;
//     QStringList datas = readTextData(path);

//     if (datas.count() < 2)
//     {
//         return blockData;
//     }

//     for (int i = 0; i < datas.count(); i++)
//     {
//         XMLFuncition functionBlockData;
//         int inNums = 0;
//         int outNums = 0;
//         functionAdd = false;
//         outAdd = false;
//         inAdd = false;

//         if (datas[i].contains(QRegExp("GLOBAL_PROTOTYP_BEGIN")))
//         {
//             if (datas[i + 1].contains(QRegExp("FUNCTION_BLOCK")))
//             {
//                 int output = 0;
//                 QStringList block = returnFunctionBlock(datas[i + 1], 1); //Qstringlist中只有一位
//                 if (block.at(0) == "")
//                 {
//                     return blockData;
//                 }
//                 functionBlockData.funcitionType = functionBlockData.FUNCITION_BLOCK;
//                 functionBlockData.type = block.at(0).trimmed();
//                 functionAdd = true;
//                 if (datas[i + 2].contains(QRegExp("VAR_INPUT"))) //读取输入
//                 {
//                     int index = 3 + i;
//                     for (int a = index; a < datas.count(); a++)
//                     {
//                         if (datas[a].contains(QRegExp("END_VAR")))
//                         {
//                             output = index;
//                             break;
//                         }
//                         index++;
//                     }                                //此处可能存在index=2
//                     for (int j = 3 + i; j < index; ++j) //读取输入  Qstringlist中有三位，一：名称 二：类型 三：默认值
//                     {
//                         inNums++;
//                         //qDebug()<<__func__<<":"<<datas[j];
//                         QStringList intputlist = returnFunctionBlock(datas[j], 0);
//                         XMLIOPut itemData;
//                         if (intputlist.size() >= 4)
//                         {

//                             itemData.name = intputlist.at(0);
//                             itemData.type = intputlist.at(1).trimmed().toUpper();
//                             //处理特殊类型
//                             if (itemData.type.contains("BOOL"))
//                             {
//                                 itemData.type = "BOOL";
//                             }
//                             itemData.initValue = intputlist.at(2);
//                             itemData.defaultValue = intputlist.at(3);
//                             itemData.index = inNums;
//                             functionBlockData.inPut.push_back(itemData);
//                         }

//                     }
//                     inAdd = true;
//                 }
//                 if (output > 0 && datas[output + 1].contains(QRegExp("VAR_OUTPUT"))) //读取输出
//                 {
//                     int outIndex = output + 1 + 1;
//                     for (int b = outIndex; b < datas.count(); b++)
//                     {
//                         if (datas[b].contains(QRegExp("END_VAR")))
//                         {
//                             break;
//                         }
//                         outIndex++;
//                     }
//                     for (int outI = output + 1 + 1; outI < outIndex;
//                         outI++) //读取输出 Qstringlist中有三位，一：名称 二：类型 三：默认值
//                     {
//                         outNums++;
//                         QStringList outputlist = returnFunctionBlock(datas[outI], 0);
//                         XMLIOPut itemData;
//                         if (outputlist.size() > 1)
//                         {
//                             itemData.name = outputlist.at(0);
//                             itemData.type = outputlist.at(1).trimmed().toUpper();
//                             //处理特殊类型
//                             if (itemData.type.contains("BOOL"))
//                             {
//                                 itemData.type = "BOOL";
//                             }
//                             itemData.index = outNums;
//                             functionBlockData.outPut.push_back(itemData);
//                         }

//                     }
//                     outAdd = true;
//                 }
//             }
//             if (functionAdd == true && outAdd == true && inAdd == true)
//             {
//                 blockData.insert(functionBlockData.type, functionBlockData);
//             }
//         }
//     }
//     return blockData;
// }

// QStringList ProBlockData::returnFunctionBlock(QString datas, bool type)
// {
//     QStringList returnData;
//     datas = datas.trimmed();
//     int lenght = datas.count();
//     if (lenght == 0)
//     {
//         returnData.push_back("");
//         return returnData;
//     }
//     if (type)
//     {
//         int namePosition = datas.indexOf("FUNCTION_BLOCK");
//         int nameLenght = lenght - namePosition;
//         QString nameData = datas.mid(namePosition + 15, nameLenght - 1).trimmed();
//         returnData.push_back(nameData);
//     }
//     else
//     {

//         QStringList initvaluelist = datas.trimmed().split(";");
//         if (initvaluelist.size() < 1)
//         {
//             return returnData;
//         }

//         QString nameData = "";
//         QString typeData = "";
//         QString initData = "";
//         QString defaultData = "";
//         QStringList tmpdatalist = initvaluelist[0].split(":");
//         if (tmpdatalist.size() < 2)
//         {
//             return returnData;
//         }
//         else
//         {
//             if (tmpdatalist.size() == 3) //有初始值
//             {
//                 nameData = tmpdatalist[0];
//                 typeData = tmpdatalist[1];
//                 initData = tmpdatalist[2].remove("=");

//             }
//             else
//             {
//                 nameData = tmpdatalist[0];
//                 typeData = tmpdatalist[1];


//             }
//         }

//         if (initvaluelist.size() > 1)
//         {
//             defaultData = initvaluelist[1].remove("=").remove(" ").remove("*").remove(":").remove("(").remove(")");
//         }
//         returnData.push_back(nameData.trimmed());
//         returnData.push_back(typeData.trimmed());
//         returnData.push_back(initData.trimmed());
//         returnData.push_back(defaultData.trimmed());
//     }
//     return returnData;
// }

// bool ProBlockData::checkBlockInfoExit(QString coreName, XMLFuncition info)
// {
//     XMLFuncition xmlFunction;
//     //reflushProFuncBlockListData();

//     if (coreName.trimmed().size() != 0)
//     {
//         //根据当前core 索引数据
//         const QMap<QString, XMLFuncition>& mapFunction = m_mapFuncBlockListData.value(coreName);
//         QMap<QString, XMLFuncition>::const_iterator iterf = mapFunction.constFind(info.type);
//         if (iterf != mapFunction.end())
//         {
//             if (compareXmlFunction(iterf.value(), info))
//             {
//                 return true;
//             }
//         }

//         // const QMap<QString, XMLFuncition> &mapFirm = m_mapFuncBlockListData.value(QString("firmware"));
//         QMap<QString, XMLFuncition>::const_iterator iterf1 = mapFunction.constFind(info.type);
//         if (iterf1 != mapFunction.end())
//         {
//             if (compareXmlFunction(iterf1.value(), info))
//             {
//                 return true;
//             }
//         }
//     }

//     //用户功能块ptt
//     const QMap<QString, XMLFuncition>& mapFunction = m_mapFuncBlockListData.value(QString("ptt"));
//     QMap<QString, XMLFuncition>::const_iterator iterf = mapFunction.constFind(info.type);
//     if (iterf != mapFunction.end())
//     {
//         if (compareXmlFunction(iterf.value(), info))
//         {
//             return true;
//         }
//     }

//     return false;
// }


// QMap<QString, XMLFuncition> ProBlockData::readPttFunctionData()
// {

//     QString pttdir = m_projectDirectory + "/$GEN$/";

//     QMap<QString, XMLFuncition> pttfunctions = readPttFunction(pttdir);
//     //readFunction(pttdir);

//     for (int pttIndex = 0; pttIndex < pttfunctions.keys().size(); pttIndex++)
//     {
//         pttfunctions[pttfunctions.keys().at(pttIndex)].mode = readFuncBlockMode(pttfunctions.keys().at(pttIndex) + ".CFC");
//     }

//     return pttfunctions;
//     //    QMap<QString, XMLFuncition> firmwareData = readFunction(functionpath);

// }

// bool ProBlockData::compareXmlFunction(XMLFuncition function1, XMLFuncition function2)
// {
//     //QList<XMLIOPut>inPutList;
//     //QList<XMLIOPut>outPutList;

//     //QList<XMLIOPut>inPutLists;
//     //QList<XMLIOPut>outPutLists;
//     const QList<XMLIOPut>& inPutLists = function1.inPut;
//     const QList<XMLIOPut>& outPutLists = function1.outPut;
//     const QList<XMLIOPut>& inPutList = function2.inPut;
//     const QList<XMLIOPut>& outPutList = function2.outPut;

//     XMLIOPut ioPut1;
//     XMLIOPut ioPut2;
//     bool bExit = false;

//     if (function1.type.toUpper() != function2.type.toUpper())
//     {
//         return false;
//     }

//     //判断类型是否对
//     if (function1.funcitionType != function2.funcitionType)
//     {
//         return false;
//     }

//     //function 对比
//     if (function1.funcitionType == XMLFuncition::FUNCITION && function2.funcitionType == XMLFuncition::FUNCITION)
//     {

//         //function 输出类型
//         if (function1.outType.toUpper() != function2.outType.toUpper())
//         {
//             return false;
//         }

//     }
//     else
//     {
//         //输入输出数量不对
//         if (function1.outPut.size() != function2.outPut.size()
//             || function1.inPut.size() != function2.inPut.size())
//         {
//             return false;
//         }

//         for (int outPutIndex = 0; outPutIndex < outPutLists.size(); outPutIndex++)
//         {
//             for (int index = 0; index < outPutList.size(); index++)
//             {
//                 ioPut1 = outPutLists.at(outPutIndex);
//                 ioPut2 = outPutList.at(index);
//                 if (ioPut1 == ioPut2)
//                 {
//                     bExit = true;
//                 }
//             }
//             if (bExit == false)
//             {
//                 return false;
//             }
//             else
//             {
//                 bExit = false;
//             }
//         }


//     }
//     for (int inPutIndex = 0; inPutIndex < inPutLists.size(); inPutIndex++)
//     {
//         for (int index = 0; index < inPutList.size(); index++)
//         {
//             ioPut1 = inPutLists.at(inPutIndex);
//             ioPut2 = inPutList.at(index);
//             if (ioPut1 == ioPut2)
//             {
//                 bExit = true;
//             }

//         }
//         if (bExit == false)
//         {
//             return false;
//         }
//         else
//         {
//             bExit = false;
//         }

//     }

//     return true;
// }

// QMap<QString, XMLFuncition> ProBlockData::readPttFunction(QString path)
// {

//     bool functionAdd = false;
//     bool varAdd = false;
//     QStringList datas = readPttTextData(path);
//     QMap<QString, XMLFuncition>firmwareData;
//     if (datas.count() < 2)
//     {
//         return firmwareData;
//     }
//     for (int i = 0; i < datas.count(); i++)
//     {
//         XMLFuncition functionData;
//         //默认赋值为N
//         functionData.mode = QString("N");
//         int nums = 0;
//         functionAdd = false;
//         varAdd = false;
//         if (datas[i].contains(QRegExp("GLOBAL_PROTOTYP_BEGIN")))
//         {
//             if (datas[i + 1].contains(QRegExp("FUNCTION")))
//             {
//                 int output = 0;
//                 QStringList functionDatas = returnFunctionDatas(datas[i + 1], 1);
//                 if (functionDatas.count() <= 1)
//                 {
//                     return firmwareData;
//                 }
//                 functionData.funcitionType = functionData.FUNCITION;
//                 functionData.type = functionDatas[0].trimmed();
//                 functionData.outType = functionDatas[1].trimmed();
//                 functionAdd = true;

//                 if (datas[i + 2].contains(QRegExp("VAR_INPUT")))
//                 {
//                     int index = i + 2;
//                     for (int a = index + 1; a < datas.count(); a++) //读取有多少个输入
//                     {
//                         if (datas[a].contains(QRegExp("END_VAR")))
//                         {
//                             output = index;
//                             break;
//                         }
//                         index++;
//                     }
//                     for (int j = 2 + i; j < index; ++j) //读取输入
//                     {
//                         nums++;
//                         QStringList intput = returnFunctionDatas(datas[j + 1], 0);

//                         XMLIOPut itemData;
//                         itemData.name = intput.at(0);
//                         itemData.type = intput.at(1).trimmed().toUpper();
//                         //处理特殊类型
//                         if (itemData.type.contains("BOOL"))
//                         {
//                             itemData.type = "BOOL";
//                         }
//                         if (itemData.type.contains(";"))
//                         {
//                             itemData.type.remove(";");
//                         }
//                         itemData.index = nums;
//                         functionData.inPut.push_back(itemData);
//                     }

//                     varAdd = true;
//                 }
//                 if (functionAdd == true && varAdd == true)
//                 {
//                     firmwareData.insert(functionData.type, functionData);
//                 }
//             }
//         }
//     }
//     return firmwareData;
// }

