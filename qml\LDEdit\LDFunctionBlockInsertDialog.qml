﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/qml/control/common"
import "qrc:/qml/control"

//功能块插入
Rectangle {
    id: control
    width: 600
    height: 400
    radius: 5

    //当前LD文件Key
    property string currentLDFile: ""

    property string currentLevelInfo: ""

    property string addtype: ""

    signal insertOK

    //标题行
    QkButtonRow {
        id: title
        Text {
            anchors.left: parent.left
            anchors.leftMargin: 20
            anchors.verticalCenter: parent.verticalCenter

            text: qsTr("Function/FunctionBlock Insert") + (trans ? trans.transString : "")
        }
    }
    Rectangle {
        anchors {
            top: title.bottom
            topMargin: 5
            left: parent.left
            leftMargin: control.width / 4
        }
        width: control.width / 2
        height: control.height - title.height - 70
        border {
            color: "gray"
            width: 1
        }
        color: "white"
        ExpandableListView {
            id: functionBlockList
            anchors.centerIn: parent
            enableReClcik: true
        }
    }

    QkButton {
        text: qsTr("Insert") + (trans ? trans.transString : "")
        width: 120
        anchors {
            right: parent.horizontalCenter
            rightMargin: 50
            bottom: parent.bottom
            bottomMargin: 25
        }
        onClicked: {
            console.log(control.currentLDFile, control.currentLevelInfo,
                        functionBlockList.selectedName)
            var ret = ""
            if (control.addtype === "Insert") {
                ret = ldManage.insertFun_FB(control.currentLDFile,
                                            control.currentLevelInfo,
                                            functionBlockList.selectedName)
            } else {
                ret = ldManage.appendFun_FB(control.currentLDFile,
                                            control.currentLevelInfo,
                                            functionBlockList.selectedName)
            }
            if (ret === "") {
                popupDialog.close()
                emit: insertOK()
            } else {
                console.log(ret)
            }
        }
    }

    QkButton {
        text: qsTr("Cancel") + (trans ? trans.transString : "")
        width: 120
        anchors {
            left: parent.horizontalCenter
            leftMargin: 50
            bottom: parent.bottom
            bottomMargin: 25
        }
        onClicked: {

            popupDialog.close()
        }
    }

    Component.onCompleted: {
        functionBlockTreeDataBind()
    }

    function functionBlockTreeDataBind() {
        functionBlockList.clearData()
        functionBlockList.expendAll = false
        var jsonary = [{
                           "parentName": "",
                           "nodeName": "项目",
                           "nodeDescription": "",
                           "nodeIcon": "folder.png",
                           "eventName": "",
                           "haveMenu": false
                       }, {
                           "parentName": "",
                           "nodeName": "Firmware",
                           "nodeDescription": "",
                           "nodeIcon": "folder.png",
                           "eventName": "",
                           "haveMenu": false
                       }, {
                           "parentName": "Firmware",
                           "nodeName": "Standard function",
                           "nodeDescription": "",
                           "nodeIcon": "folder.png",
                           "eventName": "",
                           "haveMenu": false
                       }, {
                           "parentName": "",
                           "nodeName": "Library",
                           "nodeDescription": "",
                           "nodeIcon": "folder.png",
                           "eventName": "",
                           "haveMenu": false
                       }]
        //导入基本目录
        functionBlockList.addModelDataWithJSONArray(jsonary)
        //后台拉取数据
        var datas = ldManage.getAllFunctionAndBlock()
        console.log("function block data:", JSON.stringify(datas))
        var firmfunctionblock = datas["firmfunctionblock"]
        for (var i = 0; i < firmfunctionblock.length; i++) {
            var ffb = firmfunctionblock[i]
            functionBlockList.addModelData(
                        "Firmware", ffb["name"], "", "functionBlock.png",
                        "[Standard functionBlock]" + ffb["name"], false)
        }

        var firmfunction = datas["firmfunction"]
        for (i = 0; i < firmfunction.length; i++) {
            ffb = firmfunction[i]
            functionBlockList.addModelData("Standard function", ffb["name"],
                                           "", "function.png",
                                           "[Standard function]" + ffb["name"],
                                           false)
        }

        var pptall = datas["pptall"]
        for (i = 0; i < pptall.length; i++) {
            ffb = pptall[i]
            functionBlockList.addModelData(
                        "项目", ffb["name"], "",
                        (ffb["type"] === 0 ? "function.png" : "functionBlock.png"),
                        "[pptall]", false)
        }
    }

    function init(filekey, levelinfo, addtype) {
        currentLDFile = filekey
        currentLevelInfo = levelinfo
        control.addtype = addtype
    }
}
