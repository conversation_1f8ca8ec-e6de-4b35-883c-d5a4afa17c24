﻿#include "extendfbc.h"

void ExElement::toXml(QDomDocument& doc, QDomElement& pNode)
{
    QDomElement node = doc.createElement("Element");
    node.setAttribute("Name", Name);
    node.setAttribute("ListMode", ListMode ? "true" : "false");
    node.setAttribute("dataType", dataType);

    pNode.appendChild(node);
}

void ExElement::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    Name = eNode.attribute("Name");
    ListMode = eNode.attribute("ListMode") == "true" ? true : false;
    dataType = eNode.attribute("dataType");
}

QJsonObject ExElement::toJsonObject()
{
    QJsonObject obj;
    obj["Name"] = Name;
    obj["ListMode"] = ListMode;
    obj["dataType"] = dataType;

    return obj;
}

void ExInput::toXml(QDomDocument& doc, QDomElement& pNode)
{
    QDomElement node = doc.createElement("Input");

    for (int i = 0; i < elementList.size(); i++)
    {
        elementList[i]->toXml(doc, node);
    }
    pNode.appendChild(node);
}

void ExInput::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    elementList.clear();
    QDomNodeList childs = eNode.childNodes();
    for (int i = 0; i < childs.count(); i++)
    {
        QDomNode childNode = childs.at(i);
        if (childNode.isElement())
        {
            QSharedPointer<ExElement> element = QSharedPointer<ExElement>(new ExElement());
            element->fromXml(childNode);
            elementList << element;
        }
    }
}

QJsonObject ExInput::toJsonObject()
{
    QJsonObject obj;
    QJsonArray ary;
    for (int i = 0; i < elementList.size(); i++)
    {
        ary.append(elementList[i]->toJsonObject());
    }
    obj["elements"] = ary;

    return obj;
}

void ExOutput::toXml(QDomDocument& doc, QDomElement& pNode)
{
    QDomElement node = doc.createElement("Output");

    for (int i = 0; i < elementList.size(); i++)
    {
        elementList[i]->toXml(doc, node);
    }
    pNode.appendChild(node);
}

void ExOutput::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    elementList.clear();
    QDomNodeList childs = eNode.childNodes();
    for (int i = 0; i < childs.count(); i++)
    {
        QDomNode childNode = childs.at(i);
        if (childNode.isElement())
        {
            QSharedPointer<ExElement> element = QSharedPointer<ExElement>(new ExElement());
            element->fromXml(childNode);
            elementList << element;
        }
    }
}

QJsonObject ExOutput::toJsonObject()
{
    QJsonObject obj;
    QJsonArray ary;
    for (int i = 0; i < elementList.size(); i++)
    {
        ary.append(elementList[i]->toJsonObject());
    }
    obj["elements"] = ary;

    return obj;
}

void ExInOutput::toXml(QDomDocument& doc, QDomElement& pNode)
{
    QDomElement node = doc.createElement("InOutput");

    for (int i = 0; i < elementList.size(); i++)
    {
        elementList[i]->toXml(doc, node);
    }
    pNode.appendChild(node);
}

void ExInOutput::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    elementList.clear();
    QDomNodeList childs = eNode.childNodes();
    for (int i = 0; i < childs.count(); i++)
    {
        QDomNode childNode = childs.at(i);
        if (childNode.isElement())
        {
            QSharedPointer<ExElement> element = QSharedPointer<ExElement>(new ExElement());
            element->fromXml(childNode);
            elementList << element;
        }
    }
}

QJsonObject ExInOutput::toJsonObject()
{
    QJsonObject obj;
    QJsonArray ary;
    for (int i = 0; i < elementList.size(); i++)
    {
        ary.append(elementList[i]->toJsonObject());
    }
    obj["elements"] = ary;

    return obj;
}

void ExLocal::toXml(QDomDocument& doc, QDomElement& pNode)
{
    QDomElement node = doc.createElement("Local");

    for (int i = 0; i < elementList.size(); i++)
    {
        elementList[i]->toXml(doc, node);
    }
    pNode.appendChild(node);
}

void ExLocal::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    elementList.clear();
    QDomNodeList childs = eNode.childNodes();
    for (int i = 0; i < childs.count(); i++)
    {
        QDomNode childNode = childs.at(i);
        if (childNode.isElement())
        {
            QSharedPointer<ExElement> element = QSharedPointer<ExElement>(new ExElement());
            element->fromXml(childNode);
            elementList << element;
        }
    }
}

QJsonObject ExLocal::toJsonObject()
{
    QJsonObject obj;
    QJsonArray ary;
    for (int i = 0; i < elementList.size(); i++)
    {
        ary.append(elementList[i]->toJsonObject());
    }
    obj["elements"] = ary;

    return obj;
}

void ExGlobal::toXml(QDomDocument& doc, QDomElement& pNode)
{
    QDomElement node = doc.createElement("Global");

    for (int i = 0; i < elementList.size(); i++)
    {
        elementList[i]->toXml(doc, node);
    }
    pNode.appendChild(node);
}

void ExGlobal::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    elementList.clear();
    QDomNodeList childs = eNode.childNodes();
    for (int i = 0; i < childs.count(); i++)
    {
        QDomNode childNode = childs.at(i);
        if (childNode.isElement())
        {
            QSharedPointer<ExElement> element = QSharedPointer<ExElement>(new ExElement());
            element->fromXml(childNode);
            elementList << element;
        }
    }
}

QJsonObject ExGlobal::toJsonObject()
{
    QJsonObject obj;
    QJsonArray ary;
    for (int i = 0; i < elementList.size(); i++)
    {
        ary.append(elementList[i]->toJsonObject());
    }
    obj["elements"] = ary;

    return obj;

}

void Var::toXml(QDomDocument& doc, QDomElement& pNode)
{
    QDomElement node = doc.createElement("VAR");

    input->toXml(doc, node);
    output->toXml(doc, node);
    inout->toXml(doc, node);
    local->toXml(doc, node);
    global->toXml(doc, node);

    pNode.appendChild(node);
}

void Var::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    QDomNodeList childs = eNode.childNodes();
    for (int i = 0; i < childs.count(); i++)
    {
        QDomNode childNode = childs.at(i);
        if (childNode.isElement())
        {
            if (childNode.nodeName() == "Input")
            {
                input = QSharedPointer<ExInput>(new ExInput());
                input->fromXml(childNode);
            }
            else if (childNode.nodeName() == "Output")
            {
                output = QSharedPointer<ExOutput>(new ExOutput());
                output->fromXml(childNode);
            }
            else if (childNode.nodeName() == "InOutput")
            {
                inout = QSharedPointer<ExInOutput>(new ExInOutput());
                inout->fromXml(childNode);
            }
            else if (childNode.nodeName() == "Local")
            {
                local = QSharedPointer<ExLocal>(new ExLocal());
                local->fromXml(childNode);
            }
            else if (childNode.nodeName() == "Global")
            {
                global = QSharedPointer<ExGlobal>(new ExGlobal());
                global->fromXml(childNode);
            }
        }
    }
}

QJsonObject Var::toJsonObject()
{
    QJsonObject obj;
    obj["input"] = input->toJsonObject();
    obj["output"] = output->toJsonObject();
    obj["inoutput"] = inout->toJsonObject();
    obj["local"] = local->toJsonObject();
    obj["global"] = global->toJsonObject();

    return obj;
}

void ExCode::toXml(QDomDocument& doc, QDomElement& pNode)
{
    QDomElement node = doc.createElement("CODE");
    node.setAttribute("evalCode", evalCode);

    pNode.appendChild(node);
}

void ExCode::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    evalCode = eNode.attribute("evalCode");
}

QJsonObject ExCode::toJsonObject()
{
    QJsonObject obj;
    obj["evalCode"] = evalCode;
    return obj;
}

void ExComponent::toXml(QDomDocument& doc, QDomElement& pNode)
{
    QDomElement node = doc.createElement("LDComponent");
    node.setAttribute("Name", Name);
    node.setAttribute("Enable", Enable ? 1 : 0);
    node.setAttribute("Type", Type);
    node.setAttribute("ChildType", ChildType);
    node.setAttribute("dataType", dataType);

    var->toXml(doc, node);
    code->toXml(doc, node);

    pNode.appendChild(node);
}

void ExComponent::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    Enable = eNode.attribute("Enable").toInt() > 0 ? true : false;
    Type = eNode.attribute("Type");
    Name = eNode.attribute("Name");
    ChildType = eNode.attribute("ChildType");
    dataType = eNode.attribute("dataType");

    QDomNodeList childs = eNode.childNodes();
    for (int i = 0; i < childs.count(); i++)
    {
        QDomNode childNode = childs.at(i);
        if (childNode.isElement())
        {
            if (childNode.nodeName() == "VAR")
            {
                var = QSharedPointer<Var>(new Var());
                var->fromXml(childNode);
            }
            else if (childNode.nodeName() == "CODE")
            {
                code = QSharedPointer<ExCode>(new ExCode());
                code->fromXml(childNode);
            }
        }
    }
}

QJsonObject ExComponent::toJsonObject()
{
    QJsonObject obj;
    obj["Enable"] = Enable ? 1 : 0;
    obj["Type"] = Type;
    obj["ChildType"] = ChildType;
    obj["dataType"] = dataType;
    obj["VAR"] = var->toJsonObject();
    obj["CODE"] = code->toJsonObject();

    return obj;
}

void ExComponentList::toXml(QDomDocument& doc, QDomElement& pNode)
{
    QDomElement node = doc.createElement("LDComponents");

    for (int i = 0; i < componentList.size(); i++)
    {
        componentList[i]->toXml(doc, node);
    }
    pNode.appendChild(node);
}

void ExComponentList::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();

    componentList.clear();
    QDomNodeList childs = eNode.childNodes();
    for (int i = 0; i < childs.count(); i++)
    {
        QDomNode childNode = childs.at(i);
        if (childNode.isElement())
        {
            QSharedPointer<ExComponent> comp = QSharedPointer<ExComponent>(new ExComponent());
            comp->fromXml(childNode);
            componentList << comp;
        }
    }
}

QJsonArray ExComponentList::toJsonArray()
{
    QJsonArray ary;
    for (int i = 0; i < componentList.size(); i++)
    {
        ary.append(componentList[i]->toJsonObject());
    }
    return ary;
}

void ExtendFBC::toXml(QString path)
{
    //打开或创建文件
    QFile file(path);
    if (!file.open(QFile::WriteOnly | QFile::Truncate))
    {
        return;
    }
    QDomDocument doc;
    //写入xml头部
    QDomProcessingInstruction instruction; //添加处理命令
    instruction = doc.createProcessingInstruction("xml", "version=\"1.0\" encoding=\"UTF-8\"");
    doc.appendChild(instruction);
    //添加根节点
    QDomElement root = doc.createElement("File");
    root.setAttribute("Version", Version);
    root.setAttribute("LastChange", LastChange);
    doc.appendChild(root);

    componentList->toXml(doc, root);

    //输出到文件
    QTextStream out_stream(&file);
    doc.save(out_stream, 4); //缩进4格
    file.close();
}

void ExtendFBC::fromXml(QString path)
{
    //打开或创建文件
    QFile file(path);
    if (!file.open(QFile::ReadOnly))
    {
        return;
    }

    filePath = path;

    QDomDocument doc;
    if (!doc.setContent(&file))
    {
        file.close();
        return;
    }
    file.close();

    //返回根节点
    QDomElement root = doc.documentElement();
    Version = root.attribute("Version");
    LastChange = root.attribute("LastChange");

    for (int i = 0; i < root.childNodes().size(); i++)
    {
        QDomNode node = root.childNodes().at(i);
        if (node.isElement()) //如果节点是元素
        {
            if (node.nodeName() == "LDComponents")
            {
                componentList->fromXml(node);
            }
        }
    }
}

