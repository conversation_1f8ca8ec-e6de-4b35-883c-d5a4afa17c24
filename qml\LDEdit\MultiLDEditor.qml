﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import QtQuick.Layouts 1.15
import "qrc:/qml/control/common"
import "qrc:/qml/control/table"
import "qrc:/qml/control/tab"

Rectangle {
    id: control
    property int crindex: 0
    property alias fileList: openedFileNameList
    property string deviceName: ""

    signal savefile(int fid)
    StackLayout {
        id: stacklayout
        anchors.top: control.top
        anchors.topMargin: 0
        width: control.width
        height: control.height - bar.height
        currentIndex: bar.currentIndex
        Repeater {
            model: openedFileNameList
            LDEditor {
                id: ldeditor
                fileKey: model.path
                deviceName: model.deviceName
                Component.onCompleted: {
                    control.savefile.connect(ldeditor.savefile)
                }
            }
        }
    }
    QkTabBarDown {
        id: bar
        width: control.width
        height: 40
        anchors.top: stacklayout.bottom
        anchors.topMargin: 5
        currentIndex: 0
        Repeater {
            model: openedFileNameList
            id: repeat
            QkTabButtonDown {
                id: button
                text: model.name + (model.haveChanged ? " *" : "")
                width: 150
                onClicked: {

                }
                QkButton {
                    anchors {
                        verticalCenter: parent.verticalCenter
                        right: parent.right
                        rightMargin: 5
                    }
                    width: 20
                    height: 20
                    text: "X"
                    onClicked: {
                        //关闭浏览框
                        control.closeFile(model.fileId, model.name, model.type)
                    }
                }
            }
        }
    }

    ListModel {
        id: openedFileNameList
    }

    // 文件内容被修改，添加保存标识
    Connections {
        target: ldManage
        function onDataChanged() {
            updateFileFlag(true)
        }
    }

    function updateFileFlag(flag) {
        openedFileNameList.get(bar.currentIndex)["haveChanged"] = flag
    }

    //打开编辑器, 文件列表编号, 文件名称, 文件类型, 文件路径
    function open(deviceName, fileId, name, type, filepath) {
        console.log("openFile:", deviceName, fileId, name, type,
                    openedFileNameList.count)
        var samename = false
        //读取是否成功
        console.log("readFile:", type, name)
        console.log("filepath", filepath)
        console.log(Object.prototype.toString.call(ldManage))
        if (ldManage.readFile(type, name, filepath)) {
            for (var i = 0; i < openedFileNameList.count; i++) {
                var obj = openedFileNameList.get(i)
                if (obj["fileId"] === fileId) {
                    samename = true
                    crindex = i
                    break
                }
            }
            if (!samename) {
                openedFileNameList.append({
                                              "deviceName": deviceName,
                                              "fileId": fileId,
                                              "name": name,
                                              "type": type,
                                              "path": filepath,
                                              "haveChanged": false
                                          })
                bar.currentIndex = openedFileNameList.count - 1
            } else {
                //切换到当前
                bar.currentIndex = crindex
            }
        } else {
            console.log("读取LD文件失败")
        }
    }
    //关闭文件
    function closeFile(fileId, name, type) {
        //console.log("closeFile", name, type, openedFileNameList.count)
        for (var i = 0; i < openedFileNameList.count; i++) {
            var obj = openedFileNameList.get(i)
            if (obj["fileId"] === fileId) {
                openedFileNameList.remove(i)
                bar.currentIndex = openedFileNameList.count - 1
            }
        }
    }

    //保存当前文件
    function saveCurrentFile() {
        var obj = openedFileNameList.get(bar.currentIndex)
        if (obj) {
            emit: control.savefile(obj["fileId"])
            updateFileFlag(false)
        }
    }
    //保存全部文件
    function saveAllFile() {
        emit: control.savefile(0)
        updateFileFlag(false)
    }
}
