import QtGraphicalEffects 1.15
import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/qml/control/common"
import "qrc:/qml/control/table"

Popup {
    id: control

    //文件名
    property var owned: ["Global.POE", "IOM.POE"]
    //文件分类
    property var type: ["Global", "IO", "M"]
    property string targetowned: ""
    property string targettype: ""
    property string deviceName: ""
    property variant filterSelected: []

    signal quoteVariableed

    signal variableModify(string varname)

    function getDataBind() {
        listModel.clear()
        var jsonary = VariableManage.getVariableList(control.deviceName,
                                                     control.owned,
                                                     control.type)
        const searchName = String(search_input.text).trim().toUpperCase()
        for (var i = 0; i < jsonary.length; i++) {
            var row = jsonary[i]
            if (row.name.toUpperCase().indexOf(searchName) === -1
                    || control.filterSelected.indexOf(row.name) !== -1)
                continue

            listModel.append({
                                 "rowSN": i,
                                 "isChecked": false,
                                 "isSelected": false,
                                 "isAlternate": false,
                                 "vid": row.vid,
                                 "scope": row.scope,
                                 "owned": row.owned,
                                 "type": row.type,
                                 "name": row.name,
                                 "datatype": row.dataType,
                                 "arraylength": row.arrayLength,
                                 "address": row.address,
                                 "initialvalue": row.initialValue,
                                 "description": row.description
                             })
        }
        //        const deviceIOList = DeviceAndNetworkManage.getAllShowVariablesFromDevice(control.deviceName);
        //        deviceIOList.forEach((item, index) => {
        //            if (item.name.indexOf(searchName) !== -1) {
        //                const count = listModel.count;
        //                listModel.append({
        //                    "rowSN": count + index,
        //                    "isChecked": false,
        //                    "isSelected": false,
        //                    "isAlternate": false,
        //                    "id": item.id,
        //                    "scope": "IO",
        //                    "owned": "IOM.POE",
        //                    "type": "IO",
        //                    "name": item.name,
        //                    "datatype": item.type,
        //                    "address": item.address,
        //                    "initialvalue": "",
        //                    "description": item.description
        //                });
        //            }
        //        });
    }

    width: 850
    height: 450
    modal: true //模态， 为 true后弹出窗口会叠加一个独特的背景调光效果
    focus: true //焦点,  当弹出窗口实际接收到焦点时，activeFocus将为真
    padding: 0
    closePolicy: Popup.NoAutoClose
    onOpened: {
        getDataBind()
        tableGrid.refreshWidth()
    }

    Rectangle {
        width: parent.width - 20
        anchors.top: parent.top
        anchors.bottom: table.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.topMargin: 5
        anchors.bottomMargin: 5
        anchors.leftMargin: 10
        anchors.rightMargin: 10

        Row {
            anchors.fill: parent
            spacing: 5 // 设置子元素之间的间距

            Text {
                id: search_label

                text: "名称："
                anchors.verticalCenter: parent.verticalCenter
                font.pixelSize: 15
            }

            QkTextField {
                id: search_input

                anchors.verticalCenter: parent.verticalCenter
                width: 220
                height: 32
                selectByMouse: true
                text: ""
            }

            QkButton {
                anchors.verticalCenter: parent.verticalCenter
                text: "查找"
                width: 72
                height: 32
                onClicked: {
                    getDataBind()
                }
            }
        }
    }

    Rectangle {
        id: table

        width: parent.width - 20
        height: parent.height - 50
        anchors.horizontalCenter: parent.horizontalCenter // 水平居中
        anchors.bottom: parent.bottom // 垂直对齐到父级底部

        QkTableGird {
            id: tableGrid

            anchors.top: parent.top
            needsort: false
            showCheckColumn: true
            width: parent.width
            height: parent.height - 40
            tableData: tData

            tableview.delegate: QkTableRow {
                id: tableRow

                parentControl: tableGrid.tableview
                width: tableGrid.width - 2
                rowObj: model
                widthList: tableGrid.widthList
                xList: tableGrid.xList
                onCheckedChanged: {
                    tableGrid.tableData.check(index, checked)
                }

                QkLabel {
                    text: tableRow.rowObj ? (String(
                                                 tableRow.rowObj["scope"])) : ""
                    x: tableGrid.xList[1]
                    width: tableGrid.widthList[1]
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                QkLabel {
                    text: tableRow.rowObj ? (String(
                                                 tableRow.rowObj["name"])) : ""
                    x: tableGrid.xList[2]
                    width: tableGrid.widthList[2]
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                QkLabel {
                    text: tableRow.rowObj ? (String(
                                                 tableRow.rowObj["datatype"])) : ""
                    x: tableGrid.xList[3]
                    width: tableGrid.widthList[3]
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                QkLabel {
                    text: tableRow.rowObj ? (String(
                                                 tableRow.rowObj["arraylength"]
                                                 || 1)) : ""
                    x: tableGrid.xList[4]
                    width: tableGrid.widthList[4]
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                QkLabel {
                    text: tableRow.rowObj ? (String(
                                                 tableRow.rowObj["address"])) : ""
                    x: tableGrid.xList[5]
                    width: tableGrid.widthList[5]
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                QkLabel {
                    text: tableRow.rowObj ? (String(
                                                 tableRow.rowObj["initialvalue"])) : ""
                    x: tableGrid.xList[6]
                    width: tableGrid.widthList[6]
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                QkLabel {
                    text: tableRow.rowObj ? (String(
                                                 tableRow.rowObj["description"])) : ""
                    x: tableGrid.xList[7]
                    width: tableGrid.widthList[7]
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }

        QkButton {
            anchors.top: tableGrid.bottom
            anchors.topMargin: 5
            anchors.right: parent.horizontalCenter
            anchors.rightMargin: 70
            width: 70
            height: 30
            text: "取消"
            onClicked: {
                control.close()
            }
        }

        QkButton {

            //console.log("发出quoteVariableed")
            anchors.top: tableGrid.bottom
            anchors.topMargin: 5
            anchors.left: parent.horizontalCenter
            anchors.leftMargin: 70
            width: 70
            height: 30
            text: "引入"
            onClicked: {
                //引入变量
                var flag = false
                for (var i = 0; i < listModel.count; i++) {
                    var obj = listModel.get(i)
                    if (obj["isChecked"] && obj["scope"] !== "IO") {
                        var re = VariableManage.quoteVariable(
                                    obj["vid"], control.targetowned,
                                    control.targettype)
                        flag = flag || re
                    } else if (obj["isChecked"] && obj["scope"] === "IO") {
                        var re = VariableManage.addVariable(
                            control.deviceName,
                            "IO",
                            control.targetowned,
                            control.targettype,
                            obj["name"],
                            obj["datatype"],
                            0,
                            obj["address"]
                        )
                        if (re) control.variableModify(obj["name"])
                        flag = flag || re
                    }
                }
                if (flag)
                    emit: control.quoteVariableed()

                control.close()
            }
        }
    }

    QkTableData {
        id: tData

        headerRoles: ["作用域", "名称", "数据类型", "数组长度", "地址", "初始值", "描述"]
        rowDatas: listModel
    }

    ListModel {
        id: listModel
    }

    background: Rectangle {
        color: "white"
        //color: Qt.rgba(0, 0, 0, 0) //背景为无色
        layer.enabled: true
        radius: 10

        layer.effect: DropShadow {
            transparentBorder: true
            horizontalOffset: 5
            verticalOffset: 8
        }
    }
}
