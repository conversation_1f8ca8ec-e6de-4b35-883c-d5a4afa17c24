import QtQuick 2.15
import QtQuick.Controls 2.15

Item {
    // 配置
    property var config: LDEditorConfiger
    // 主控件
    property var mainControl
    // 连线x起点坐标
    property int connectionStartX
    // 连线y起点坐标
    property int connectionStartY
    // 链接
    property var connections
    // 存放每个块元件的所有属性
    property var blockItems
    // 坐标轴
    property var points: []
    // 线条颜色
    property color lineColor: config.defaultConnectionLineColor

    onBlockItemsChanged: {
        calculateConnectionPoints()
    }

    onWidthChanged: {
        if(blockItems)
        {
            // 宽度变化的时候需要重新计算连线的坐标点
            calculateConnectionPoints()
        }
    }

    Canvas {
        id: canvas
        anchors.fill: parent
        // 画布对象
        property var ctx
        onPaint: {
            // 获取画布对象
            ctx = getContext("2d")
            // 清空画布内容
            ctx.clearRect(0, 0, width, height)
            // 设置线条宽度
            ctx.lineWidth = config.defaultLineWidth
            ctx.lineJoin = "round"
            // 防止线条断裂,导致绘制出来的少像素
            ctx.lineCap = "square"
            // 线条重叠时颜色加深
            ctx.globalCompositeOperation = "multiply"
            
            for (let pIndex = 0; pIndex < points.length; pIndex++)
            {
                const p = points[pIndex]
                const x1 = p.startX
                const y1 = p.startY
                const x2 = p.endX
                const y2 = p.endY
                const offset = p.offset
                const isLeftToRight = x1 < x2
                
                // 线条颜色
                ctx.strokeStyle = reverseForegroundColor(p.lineColor)

                ctx.beginPath()
                ctx.moveTo(x1, y1)

                if (isLeftToRight)
                {
                    if(offset > 0)
                    {
                        ctx.lineTo(x2 - offset, y1)
                        ctx.lineTo(x2 - offset, y2)
                        ctx.lineTo(x2, y2)
                    }
                    else
                    {
                        ctx.lineTo(x2, y1)
                        ctx.lineTo(x2, y2)
                    }
                }
                else
                {
                    ctx.lineTo(x1, y2)
                    ctx.lineTo(x2, y2)
                }
                
                ctx.stroke()
            }
        }
    }

    // 根据混合结果颜色和透明度，反推出前景色
    function reverseForegroundColor(hexColor, alpha = 0.5, background = [255, 255, 255]) {
        // 确保传入的是字符串类型
        hexColor = String(hexColor)

        // 提前颜色
        const r = parseInt(hexColor.slice(1, 3), 16)
        const g = parseInt(hexColor.slice(3, 5), 16)
        const b = parseInt(hexColor.slice(5, 7), 16)
        // 混合结果颜色
        const resultColor = [r, g, b]

        const fg = resultColor.map((c, i) => {
            const value = (c - background[i] * (1 - alpha)) / alpha
            // 保证结果在 [0, 255] 范围内，并四舍五入为整数
            return Math.max(0, Math.min(255, Math.round(value)))
        })

        return Qt.rgba(fg[0] / 255, fg[1] / 255, fg[2] / 255, alpha)
    }

    // 计算连线的坐标点
    function calculateConnectionPoints()
    {
        const newPoints = []
        // 连线终点组件,用于筛选出线圈、跳转、返回所在的组件
        const destinationComponents = []
        // 连线起点组件
        let sourceComponent
        // 连线终点组件
        let targetComponent
        
        for(let connIndex = 0; connIndex < connections.length; connIndex++)
        {
            // 连线起点块元件号
            const sourceNumber = connections[connIndex].SourceComponentNumber
            // 连线终点块元件号
            const targetNumber = connections[connIndex].TargetComponentNumber
            const sourcePinId = connections[connIndex].SourcePinId
            const targetPinId = connections[connIndex].TargetPinId

            for(let bIndex = 0; bIndex < blockItems.length; bIndex++)
            {
                // 块元件所在的组件
                const blockItem = blockItems[bIndex]
                // 块元件号
                const blockNumber = blockItem.blockData.Number

                if(blockNumber === sourceNumber)
                {
                   sourceComponent = blockItem
                }

                if(blockNumber === targetNumber)
                {
                    targetComponent = blockItem
                    destinationComponents.push(targetComponent)
                }
            }

            // 起点块元件的中心所在高度
            const sourceCentreHeight = (sourceComponent.blockData.Height * config.cellHeight - config.defaultLineWidth) / 2
            // 终点块元件的中心所在高度
            const targetCentreHeight = (targetComponent.blockData.Height * config.cellHeight - config.defaultLineWidth) / 2
            
            const startX = sourceComponent.x + connectionStartX + sourceComponent.width - config.defaultLineWidth / 2
            const startY = calculateY(sourceComponent, sourceCentreHeight)
            const endX = targetComponent.x + connectionStartX - config.defaultLineWidth / 2
            const endY = calculateY(targetComponent, targetCentreHeight)

            if(startX !== endX || startY !== endY)
            {
                // console.log("sourceNumber:", sourceNumber)
                // console.log("XPos:", sourceComponent.blockData.XPos)
                // console.log("YPos:", sourceComponent.blockData.YPos)
                // console.log("targetNumber:", targetNumber)
                // console.log("XPos:", targetComponent.blockData.XPos)
                // console.log("YPos:", targetComponent.blockData.YPos)
                // console.log("======================================")

                if(sourceComponent.blockData.XPos === targetComponent.blockData.XPos)
                {
                    // 在同一条x轴上,那绘制的线条应该是一条竖线
                    newPoints.push({ "source": sourceNumber, "target": targetNumber, "startX": startX, 
                                     "startY": startY, "endX": startX, "endY": endY, "lineColor": lineColor,
                                     "offset": 0 })
                }
                else
                {
                    let offset = 0

                    // 目标块元件所在的y轴上是否存在扩展块元件
                    if(isExistExpandBlock(targetComponent.blockData.XPos, targetComponent.blockData.YPos, true, false))
                    {
                        // 存在的扩展块元件是否是紧挨着目标块元件的上下方
                        for(let cIndex = 0; cIndex < components.length; cIndex++)
                        {
                            const component = components[cIndex]
                            
                            if(component.XPos === targetComponent.blockData.XPos && 
                               isFuncOrFuncBlock(component)
                              )
                            {
                                // 只有在块元件紧挨着扩展块元件的时候,连线则需要从扩展块元件边界走
                                offset = config.defaultBlockWidth * config.cellWidth
                            }
                        }
                    }

                    newPoints.push({ "source": sourceNumber, "target": targetNumber, "startX": startX, 
                                     "startY": startY, "endX": endX, "endY": endY, "lineColor": lineColor,
                                     "offset": offset })
                }
            }
        }
        
        // 为线圈、跳转、返回添加结束线条
        newPoints.push(...addFinalLine(destinationComponents))
        // 在没有线圈、跳转、返回元件的时候为块元件添加结束线条
        newPoints.push(...calculateFinalLine())
        points = newPoints
        // console.log("points:", JSON.stringify(points))
        canvas.requestPaint()
    }

    // 计算y轴
    function calculateY(blockItem, centreHeight)
    {
        const component = blockItem.blockData

        if(isFuncOrFuncBlock(component))
        {
            // posTextHeight 坐标文本高度
            // funBlockTextHeight 块元件文本高度
            // 1 线条高度取中间值
            return blockItem.y + connectionStartY + config.defaultDescHeight + 
                   config.defaultMargin + posTextHeight + funBlockTextHeight + cellCentreHeight - config.defaultLineWidth / 2
        }
        
        return blockItem.y + connectionStartY + config.defaultDescHeight + config.defaultMargin + centreHeight
    }

    // 为线圈、跳转、返回添加结束线条,在有些情况下线圈、跳转、返回的位置并不是紧靠着整个网络的边框的
    // 所以需要添加一条结束线条来标识
    function addFinalLine(destinationComponents)
    {
        // 结束线条点位
        const endPoints = []
        // 已经处理过的块元件对应的number
        const handleNumbers = []

        for(let dIndex = 0; dIndex < destinationComponents.length; dIndex++)
        {
            // 块元件所在的组件
            const blockItem = destinationComponents[dIndex]
            // 块元件数据
            const blockData = blockItem.blockData
            // 获取块元件类型
            const blockType = getBlockType(blockData)
            // 校验是否已经处理过该number对应的块元件
            if(!handleNumbers.includes(blockData.Number))
            {
                // 校验块元件类型是否是线圈、跳转、返回其中的一种
                // 如果是则需要添加结束线条
                if(blockType === "coil" || blockType === "jump" || 
                   blockType === "return" || blockType === "set0" || 
                   blockType === "set1")
                {
                    // 块元件的中心所在高度
                    const blockHeight = (blockData.Height * config.cellHeight - config.defaultLineWidth) / 2
                    const startX = blockItem.x + connectionStartX + blockItem.width - 1
                    const startY = blockItem.y + connectionStartY + config.defaultDescHeight + config.defaultMargin + blockHeight
                    const endX = width - config.defaultLineWidth - 1
                    const endY = startY
                    handleNumbers.push(blockData.Number)
                    endPoints.push({ "source": blockData.Number, "target": blockData.Number, "startX": startX, 
                                     "startY": startY, "endX": endX, "endY": endY, "lineColor": lineColor,
                                     "offset": 0 })
                }
            }
        }

        return endPoints
    }

    // 计算结束线条
    function calculateFinalLine()
    {
        // 不包含or块元件的最大x轴
        const notIncludeOrMaxXPosList = findMaxXInYGroup(false)
        const points = []
        let isExist = false
        
        for(let bIndex = 0; bIndex < blockItems.length; bIndex++)
        {
            isExist = false
            const blockItem = blockItems[bIndex]
            const blockData = blockItem.blockData
            
            if(notIncludeOrMaxXPosList[blockData.YPos] === blockData.XPos && !isFlowEndBlock(getBlockType(blockData)))
            {
                for(let connIndex = 0; connIndex < connections.length; connIndex++)
                {
                    const sourceNumber = connections[connIndex].SourceComponentNumber
                    
                    if(sourceNumber === blockData.Number)
                    {
                        isExist = true
                        break
                    }
                }

                // 只有在块元件没有连线的情况下才添加结束线条
                if(!isExist)
                {
                    const funcType = getBlockType(blockData)
                    let startX = 0
                    let startY = 0
                    let blockHeight = 0

                    if(funcType === "func" || funcType === "fb" || funcType === "advance")
                    {
                        startX = blockItem.x + connectionStartX + blockItem.width - 1
                        startY = blockItem.y + connectionStartY + funBlockTextHeight + posTextHeight + cellCentreHeight + config.defaultDescHeight + config.defaultMargin - config.defaultLineWidth / 2
                    }
                    else
                    {
                        // 块元件中心高度
                        blockHeight = (blockData.Height * config.cellHeight - config.defaultLineWidth) / 2
                        startX = blockItem.x + connectionStartX + blockItem.width - 1
                        startY = blockItem.y + connectionStartY + config.defaultDescHeight + config.defaultMargin + blockHeight
                    }

                    const endX = width - config.defaultLineWidth - 1
                    const endY = startY

                    points.push({ "source": blockData.Number, "target": blockData.Number, "startX": startX, 
                                  "startY": startY, "endX": endX, "endY": endY, "lineColor": lineColor,
                                  "offset": 0 })
                }
            }
        }

        // console.log("points:", JSON.stringify(points))
        return points
    }
}

