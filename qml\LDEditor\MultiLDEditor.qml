﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtGraphicalEffects 1.15
import QtQuick.Layouts 1.15
import "qrc:/qml/control/common"
import "qrc:/qml/control/table"
import "qrc:/qml/control/tab"

Rectangle {
    id: control
    property int crindex: 0
    property alias fileList: openedFileNameList
    property string deviceName: ""

    // 缩放比例
    property int scaleFactor: 100

    function getScopeListFromType(type) {
        var scopelist = ["Local"]
        if (type === "FUNCTIONBLOCK") {
            scopelist.push("Input")
            scopelist.push("Output")
            scopelist.push("Static")
        } else if (type === "FUNCTION") {
            scopelist.push("Input")
            scopelist.push("Output")
        }
        return scopelist
    }

    StackLayout {
        id: stacklayout
        anchors.top: control.top
        anchors.topMargin: 0
        width: control.width
        height: control.height - bar.height
        currentIndex: bar.currentIndex

        Repeater {
            id: ld_editor_views
            model: openedFileNameList
            LDEditorView {
                dataTypeList: VariableManage.getDataType(
                                  model.deviceName).filter(function (item) {
                                      return model.type === "FUNCTION" ? (item.type === "BASE") : (item.type === "BASE" || item.type === "USER")
                                  })
                scopeList: getScopeListFromType(model.type)
                deviceName: model.deviceName
                fileName: model.name
                fileType: model.type
                fileId: model.fileId
                fileKey: model.path
                isConserve: model.haveChanged
            }
        }
    }

    QkTabBarDown {
        id: bar
        width: control.width
        height: 40
        anchors.top: stacklayout.bottom
        anchors.topMargin: 5
        currentIndex: 0

        Repeater {
            id: repeat
            model: openedFileNameList

            QkTabButtonDown {
                id: button
                text: model.name + (model.haveChanged ? " *" : "")
                width: 150

                QkButton {
                    width: 20
                    height: 20
                    text: "X"
                    anchors {
                        verticalCenter: parent.verticalCenter
                        right: parent.right
                        rightMargin: 5
                    }
                    onClicked: {
                        //关闭浏览框
                        control.closeFile(model.fileId, model.name, model.type)
                    }
                }
            }
        }
    }

    Rectangle {
        width: 350
        height: 30
        anchors.top: stacklayout.bottom
        anchors.topMargin: 5
        anchors.right: stacklayout.right
        Row {
            anchors.fill: parent
            Rectangle {
                width: 150
                height: parent.height
                border.color: "#7a7a7a"
                border.width: 1
                SpinBox {
                    id: spin_box
                    width: parent.width
                    height: parent.height
                    from: 50
                    to: 200
                    value: scaleFactor
                    stepSize: 1
                    onValueChanged: {
                        scaleFactor = value
                    }
                }
            }

            Slider {
                id: slider
                width: 200
                height: parent.height
                from: 50
                value: scaleFactor
                to: 200
                Keys.onLeftPressed: event.accepted = true
                Keys.onRightPressed: event.accepted = true

                onValueChanged: {
                    scaleFactor = value
                }

                background: Rectangle {
                    x: slider.leftPadding
                    y: (slider.height - height) / 2
                    width: slider.availableWidth
                    height: 4
                    color: "#cccccc"
                }

                handle: Rectangle {
                    x: parent.leftPadding + (parent.horizontal ? parent.visualPosition * (parent.availableWidth - width) : (parent.availableWidth - width) / 2)
                    y: parent.topPadding
                       + (parent.horizontal ? (parent.availableHeight - height)
                                              / 2 : parent.visualPosition
                                              * (parent.availableHeight - height))
                    implicitWidth: 10
                    implicitHeight: 20
                    color: "#007ad9"
                }
            }
        }
    }

    ListModel {
        id: openedFileNameList
    }

    function updateFileFlag(flag) {
        openedFileNameList.get(bar.currentIndex)["haveChanged"] = flag
    }

    //打开编辑器, 文件列表编号, 文件名称, 文件类型, 文件路径
    function open(deviceName, fileId, name, type, filepath) {
        console.log("openFile:", deviceName, fileId, name, type,
                    openedFileNameList.count)
        var samename = false
        //读取是否成功
        console.log("readFile:", type, name)
        console.log("filepath", filepath)
        console.log(Object.prototype.toString.call(ldManage))
        if (ldManage.readFile(deviceName, type, name, filepath)) {
            // 一次只能打开一个窗口
            // 重置缩放比例
            scaleFactor = 100
            openedFileNameList.clear()
            openedFileNameList.append({
                                          "deviceName": deviceName,
                                          "fileId": fileId,
                                          "name": name,
                                          "type": type,
                                          "path": filepath,
                                          "haveChanged": false
                                      })
        } else {
            console.log("读取LD文件失败")
        }
    }

    //关闭文件
    function closeFile(fileId, name, type) {
        //console.log("closeFile", name, type, openedFileNameList.count)
        for (var i = 0; i < openedFileNameList.count; i++) {
            var obj = openedFileNameList.get(i)
            if (obj["fileId"] === fileId) {
                openedFileNameList.remove(i)
                bar.currentIndex = openedFileNameList.count - 1
            }
        }
    }

    //保存当前文件
    function saveCurrentFile() {
        var obj = openedFileNameList.get(bar.currentIndex)
        if (obj) {
            ldManage.saveFile(obj["path"])
            updateFileFlag(false)
        }
    }

    //保存全部文件
    function saveAllFile() {
        ldManage.saveAllFile()
        updateFileFlag(false)
    }
}
