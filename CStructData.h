﻿#ifndef CSTRUCTDATA_H
#define CSTRUCTDATA_H
#include <QString>
#include <QList>

#include <QString>
#include <QList>
#include <QByteArray>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QTextStream>
#include <QDebug>
#include <QIODevice>
#include <QFile>

#include "ccompatibletype.h"

#define DATA_COUNT 19
//#define RESOURCE_PATH ":/libs/Modules"
//
#define PROJECT_IMAGE ":/libs/create_image/BMP00004.BMP"
#define TREEIMAGE_FILE ":/libs/create_image/FOLDER_C.BMP"
#define TREEIMAGE_PROJECT ":/libs/create_image/TASK_INT.BMP"
#define TREEIMAGE_CPU ":/libs/create_image/TASK_TIM.BMP"
#define TREEIMAGE_CORE ":/libs/create_image/TASK_UNK.BMP"
#define POE_ERROR ":/libs/create_image/global_block.png"

//CFC文件类型
#define PRO_FILE "PROGRAM"
#define FB_FILE "FUNCTION_BLOCK"

//文件类型
#define GLOBAL_FILE "GLOBAL"
#define SHAREDMEMORY "SharedMemory"
#define CFC_FILE "CFC"
#define CFCFB_FILE "CFCFB"

//功能块接口引脚最大值
#define FB_INTERFACE_MAX 14

class CBrokenLineItem;
class QGraphicsItem;

using LinkLine = QList<QLineF>;

class TypeTranslate
{
public:

    static QString typeToAbbreviation(QString type)
    {
        type = type.trimmed().toUpper();
        if (type == "BOOL")
        {
            return "B1";
        }
        else if (type == "SINT")
        {
            return "SI";
        }
        else if (type == "USINT")
        {
            return "US";
        }
        else if (type == "INT")
        {
            return "I";
        }
        else if (type == "DINT")
        {
            return "DI";
        }
        else if (type == "UINT")
        {
            return "UI";
        }
        else if (type == "UDINT")
        {
            return "UD";
        }
        else if (type == "REAL")
        {
            return "R";
        }
        else if (type == "LREAL")
        {
            return "LR";
        }
        else if (type == "TIME")
        {
            return "T";
        }
        else if (type == "BYTE")
        {
            return "BY";
        }
        else if (type == "WORD")
        {
            return "W";
        }
        else if (type == "DWORD")
        {
            return "DW";
        }
        else if (type == "STRING")
        {
            return "S";
        }
        else if (type == "WSTRING")
        {
            return "WS";
        }
        else
        {
            return "ERROR";
        }
    }

    static QString typeFormAbbreviation(QString type)
    {
        type = type.trimmed().toUpper();
        if (type == "B1")
            return "BOOL";
        else if (type == "SI")
            return "SINT";
        else if (type == "US")
            return "USINT";
        else if (type == "I")
            return "INT";
        else if (type == "DI")
            return "DINT";
        else if (type == "UI")
            return "UINT";
        else if (type == "UD")
            return "UDINT";
        else if (type == "R")
            return "REAL";
        else if (type == "LR")
            return "LREAL";
        else if (type == "T")
            return "TIME";
        else if (type == "BY")
            return "BYTE";
        else if (type == "W")
            return "WORD";
        else if (type == "DW")
            return "DWORD";
        else if (type == "S")
            return "STRING";
        else if (type == "WS")
            return "WSTRING";
        else
            return "ERROR";
    }
    static bool pinTypeEx(QString typeA, QString typeB)
    {
        QMap<QString, QStringList> Compatible = CCompatibleType::instance()->getCompatibleTypeData();
        QStringList compatibleList = Compatible[typeFormAbbreviation(typeA)];
        compatibleList.append(typeFormAbbreviation(typeA));

        return compatibleList.contains(typeFormAbbreviation(typeB));
    }
};

//窗口类型
enum WidgetType
{
    NONEWidget,
    CFCWidget,
    GLBWidget,
    IoWidget,
    GlobalWidget,
    SharedVariable,
    MultiCoreSharedVariable,
    TYPWidget,
    ILWidget,
    STWidget
};

//输入输出
enum IOType
{
    InPut,
    OutPut
};

struct PageState
{
    int pageNum;
    uint32_t pageId;
    int pageType;
};

//引脚数据
struct ConnectorData
{
    //输入输出类型
    IOType ioType;
    //名字
    QString name;
    //类型
    QString type;
    //默认值
    QString value;
    //初始值
    QString initValue;
    //注释
    QString comment;
    //快速信号
    bool fastSignal;
    //是否可用
    bool fastSignalEnabled;
    //是否是初始值
    bool isInitVar;
    //Id
    uint8_t id;

    //是否被连接（编辑块属性时使用）
    bool isLink;

    ConnectorData()
        :fastSignal(false)
        , fastSignalEnabled(false)
    {
    }

};

//块数据
struct BlockData
{
    //块类型
    QString blockType;
    //块名字
    QString blockName;
    //块名字Id
    int id;
    //任务名
    QString taskName;
    //注释
    QString annotation;
    //引脚
    QList<ConnectorData> connectors;

    //文本块复制粘贴时使用
    //宽
    int width;
    //高
    int height;

    //函数类型(0:函数，1：函数块)
    int funType;
};

struct ClipboardLinkPath
{
    uint64_t blockId;
    QString pinName;
};

struct ClipboardLink
{
    QString pinName;

    QList<ClipboardLinkPath> linkToInfos;
};

struct SaveChart;
//剪贴板块数据
struct ClipboardBlockData
{
    //数据
    BlockData boockData;
    //相对位置(左上)
    QPointF relativePos;
    //相对右下坐标
    QPointF relativeBRPos;

    //相对id
    uint64_t blockId;

    //模型
    QString mode;

    //链接
    QList<ClipboardLink> pinLinks;
    //复合块的Chart数据
    SaveChart* chart;
};

//剪贴板
struct Clipboard
{
    WidgetType type;
    //cfc数据
    QList<ClipboardBlockData> CFCData;
    //全局变量数据
    QString GLBData;

    Clipboard() :type(NONEWidget) {}
};

//chart的打印信息
struct ChartPrintData
{
    //路径
    QString path;
    //注释
    QString comment;

    //起始页数
    int startPageNum;

    //chart的名字
    QString chartName;
    //页图片集合
    QList<QPixmap> pages;

    //当前chart下的子chart（复合块）
    QList<ChartPrintData> childChart;
};

//打印目录信息
struct PrintCatalogueInfo
{
    //起始页
    int sPageNum;
    //页数
    int pagesNum;
    //chart名
    QString cName;
    //层级
    int chartlevel;

    PrintCatalogueInfo(int num, int pNum, QString name, int level)
        :sPageNum(num), pagesNum(pNum), cName(name), chartlevel(level)
    {
    }
};

#endif
