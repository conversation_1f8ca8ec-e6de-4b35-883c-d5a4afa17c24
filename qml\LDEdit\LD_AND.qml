﻿import QtQuick 2.15


//LD 与块
Rectangle {
    id: control

    //主控件
    property var mainControl
    //配置
    property var config: LDEditorConfig
    //线条起点的偏移量
    property int lineOffset: config.defaultLineHeightOffset + config.defaultConnectorHeight
                             + config.defaultConnectorMargin
    //绑定的数据
    property var bindData

    color: "transparent"

    //EN ENO引脚
    Repeater {
        model: bindData.Connectors
        Rectangle {
            id: enoline
            visible: model.Type === "EN" || model.Type === "ENO"
            width: config.defaultENENOWidth
            height: config.defaultLineWidth
            x: model.Type === "EN" ? 0 : c_bl.width + config.defaultENENOWidth
            y: control.lineOffset
            color: mainControl.isOnline ? (model.Value ? config.defaultLineTrueColor : config.defaultLineFalseColor) : config.defaultLineColor
            //引脚的标签名称
            Text {
                anchors {
                    horizontalCenter: parent.horizontalCenter
                    top: parent.top
                    topMargin: -config.fontPixelSize
                }
                visible: config.isShowLabelName
                font.pixelSize: config.fontPixelSize
                text: model.LabelName
            }
        }
    }

    Text {
        anchors {
            right: parent.right
            bottom: parent.bottom
        }
        font.pixelSize: config.fontPixelSize
        color: "lightgray"
        text: "AND"
    }

    //与块没有变量与注释
    Row {
        id: c_bl
        anchors {
            left: parent.left
            leftMargin: config.defaultENENOWidth
            top: parent.top
            topMargin: 0
        }
        Repeater {
            model: bindData.Blocks
            LDBlock {
                id: bl
                mainControl: control.mainControl
                bindData: model
                function reSize() {
                    var t = 0
                    var tempwidth = 0
                    var tempheight = 0
                    for (var i = 0; i < c_bl.children.length; i++) {
                        if (c_bl.children[i] instanceof LDBlock) {
                            if (t === 0) {
                                tempwidth = c_bl.children[i].width
                                tempheight = c_bl.children[i].height
                            } else {
                                tempwidth += c_bl.children[i].width
                                if (tempheight < c_bl.children[i].height)
                                    tempheight = c_bl.children[i].height
                            }
                            t++
                        }
                    }
                    control.width = tempwidth + config.defaultENENOWidth * 2
                    control.height = tempheight
                }
                Component.onCompleted: {
                    reSize()
                }
                onHeightChanged: {
                    reSize()
                }
                onWidthChanged: {
                    reSize()
                }
            }
        }
    }
}
