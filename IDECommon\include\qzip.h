﻿/*
 * @Author: liquan
 * @Date: 2024-03-19 15:38:32
 * @Last Modified by: liquan
 * @Last Modified time: 2024-03-29 15:38:46
 */

#ifndef QZIP_H
#define QZIP_H

#include <QObject>
#include <QString>
#include "IDEV1_Common_global.h"

class IDEV1_COMMON_EXPORT QZip : public QObject
{
    Q_OBJECT
public:
    static QZip &instance();

    // 加密压缩文件
    bool zip(const QString &filePath, const QString &dirPaths, const QString &password);
    // 解压文件
    QStringList unzip(const QString &filePath, const QString &dirPath, const QString &password);

private:
};

#endif
