﻿import QtQuick 2.15
import QtQuick.Controls 1.4
import "qrc:/qml/control/common"

//功能插入
Rectangle {
    id: control
    width: 600
    height: 300
    radius: 5

    //当前LD文件Key
    property string currentLDFile: ""

    //标题行
    QkButtonRow {
        id: title
        Text {
            anchors.left: parent.left
            anchors.leftMargin: 20
            anchors.verticalCenter: parent.verticalCenter

            text: qsTr("Function Insert") + (trans ? trans.transString : "")
        }
    }

    function init(filekey) {
        currentLDFile = filekey
    }
}
