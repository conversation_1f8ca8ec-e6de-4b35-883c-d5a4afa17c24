import QtQuick 2.15
import QtQuick.Controls 2.15

Rectangle {
    id: control
    // 主控件
    property var mainControl
    // 当前所在的网络组件
    property var netWorkControl
    // 配置
    property var config: LDEditorConfiger
    // 块数据
    property var blockData
    // 记录当前选中的网络中对应的start块元件序号
    property int startBlockNumber
    // 当前所在的网络号
    property int netWorkNumber
    // 置反
    property bool negated: false

    width: blockData.Width * config.cellWidth
    height: blockData.Height * config.cellHeight
    color: "transparent"

    Rectangle {
        width: parent.width - config.defaultLineWidth
        height: parent.height
        color: isShear ? "transparent" : (parent.parent.focus ? "#6495ed" : "transparent")
    }

    Text {
        visible: isTest && blockData.Number !== startBlockNumber
        text: "(" + blockData.XPos + "," + blockData.YPos + ")"
        font.pixelSize: config.fontPixelSize
        anchors.horizontalCenter: parent.horizontalCenter
    }

    Row {
        anchors.fill: parent
        Rectangle {
            anchors.top: parent.top
            anchors.topMargin: parent.height / 2 - config.defaultLineWidth
            height: config.defaultLineWidth
            width: blockData.Width / 8 * 2 * config.cellWidth
            color: config.defaultConnectionLineColor
            // 引脚置反
            Rectangle {
                width: config.defaultLineWidth * 4
                height: config.defaultLineWidth * 4
                radius: height / 2
                border.width: 1
                border.color: "#000000"
                color: "#ffffff"
                anchors.right: parent.right
                y: -height / 2 + config.defaultLineWidth / 2
                visible: negated
            }
        }

        Row {
            anchors.verticalCenter: parent.verticalCenter
            height: parent.height
            width: blockData.Width / 8 * 4 * config.cellWidth

            Rectangle {
                height: parent.height
                width: parent.width / 8 * 1
                color: "transparent"
                Text {
                    text: "("
                    font.pixelSize: 20
                    color: "#000000"
                    anchors.centerIn: parent
                    anchors.verticalCenterOffset: -2
                    anchors.horizontalCenterOffset: -1
                }
            }

            Rectangle {
                height: parent.height
                width: parent.width / 8 * 6
                color: "transparent"
                Text {
                    text: getCoilText()
                    font.pixelSize: 20
                    color: "#000000"
                    anchors.centerIn: parent
                    anchors.verticalCenterOffset: -1
                }
            }

            Rectangle {
                height: parent.height
                width: parent.width / 8 * 1 + 1
                color: "transparent"
                Text {
                    text: ")"
                    font.pixelSize: 20
                    color: "#000000"
                    anchors.centerIn: parent
                    anchors.verticalCenterOffset: -2
                    anchors.horizontalCenterOffset: 1
                }
            }
        }

        Rectangle {
            anchors.top: parent.top
            anchors.topMargin: parent.height / 2 - config.defaultLineWidth
            height: config.defaultLineWidth
            width: blockData.Width / 8 * 2 * config.cellWidth
            color: config.defaultConnectionLineColor
        }
    }

    MouseArea {
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        // 长按时间为50毫秒
        pressAndHoldInterval: 100
        property bool isLongPress: false
        // 开始长按时的坐标
        property int startLongPressX: 0
        property int startLongPressY: 0
        onClicked: {
            activeFocusItem = "BLOCK"
            parent.forceActiveFocus()
            mainControl.startBlockNumber = control.startBlockNumber
            mainControl.netWorkNumber = control.netWorkNumber
            if (mouse.button === Qt.RightButton)
            {
                const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                mainControl.showMenu(positionInRoot.x, positionInRoot.y, "BLOCK", getBlockType(blockData), blockData,
                                     includeOrMaxXPosList[blockData.YPos] === blockData.XPos)
            }
            else
            {
                mainControl.currentSelectBlockType = getBlockType(blockData)
                mainControl.currentSelectBolckData = blockData
            }
        }
        onPressAndHold:  {
            activeFocusItem = "BLOCK"
            parent.forceActiveFocus()
            mainControl.startBlockNumber = control.startBlockNumber
            mainControl.netWorkNumber = control.netWorkNumber
            const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
            if (mouse.button === Qt.RightButton)
            {
                mainControl.showMenu(positionInRoot.x, positionInRoot.y, "BLOCK", getBlockType(blockData), blockData,
                                     includeOrMaxXPosList[blockData.YPos] === blockData.XPos)
            }
            else if(mouse.button === Qt.LeftButton)
            {
                mainControl.currentSelectBlockType = getBlockType(blockData)
                mainControl.currentSelectBolckData = blockData
                // 开启了长按
                isLongPress = true
                // 当前长按的块元件id
                mainControl.currentSelectBolckData = blockData
                startLongPressX = positionInRoot.x
                startLongPressY = positionInRoot.y
            }
        }
        onReleased: {
            // 关闭了长按
            isLongPress = false
            mainControl.isShowPositionBlocks = false
            startLongPressX = 0
            startLongPressY = 0

            if(Object.keys(netWorkControl.currentSelectPositionBlock).length !== 0)
            {
                console.log("Number:", blockData.Number)
                console.log("currentSelectPositionBlock:", JSON.stringify(netWorkControl.currentSelectPositionBlock))

                netWorkControl.currentSelectPositionBlock = {}
            }
        }
        onPositionChanged: {
            if(isLongPress && !isFlowEndBlock(getBlockType(blockData)))
            {
                if(startLongPressX !== 0 && startLongPressY !== 0)
                {
                    // 计算与开始长按时的坐标的差值
                    const positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                    const deltaX = Math.abs(positionInRoot.x - startLongPressX)
                    const deltaY = Math.abs(positionInRoot.y - startLongPressY)

                    // 只有在长按的情况下并且大幅度移动时才会显示坐标块
                    if(deltaX >= 20 || deltaY >= 20)
                    {
                        mainControl.isShowPositionBlocks = true
                        const positionInNetWork = mapToItem(netWorkControl, mouse.x, mouse.y)
                        if(positionInNetWork.x < 0 || positionInNetWork.y < 0)
                        {
                            mainControl.isShowPositionBlocks = false
                        }
                        else
                        {
                            // 触发信号,重新计算坐标块的位置
                            calculatePositionBlockPoints(positionInNetWork.x, positionInNetWork.y)
                        }
                    }
                }
            }
        }
    }

    function getCoilText()
    {
        negated = false
        
        switch(getBlockType(blockData))
        {
            case "set0": 
                return "R"
            case "set1": 
                return "S"
            case "jump":
                negated = blockData.Negated
                return "JMP"
            case "return":
                negated = blockData.Negated
                return "RET"
            default:
                if(blockData.Negated)
                {
                    // 置反
                    return "/"
                }
                return ""
        }
    }
}