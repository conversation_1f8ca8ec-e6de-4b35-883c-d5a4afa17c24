﻿import QtQuick 2.15
import QtQuick.Dialogs 1.3
import "qrc:/qml/gv"
import "qrc:/qml/skin"
import "qrc:/qml/control/common"
import "qrc:/qml/control/menu"

//LD编辑器主框架
Rectangle {
    id: control

    //anchors.fill: parent
    width: parent.width
    height: parent.height
    clip: true
    color: "white"

    //对应LD文件键值
    property string deviceName: ""
    property string fileKey: "Core1/LD001.LD"

    //单个段落可以支持的最大块数量
    property int maxBlockCount: 200

    //配置
    property var config: LDEditorConfig
    //绑定的JSON数据
    property var bindData

    property var selectID
    //是否在线
    property bool isOnline: false

    property alias mainroot: root

    //悬浮按钮窗口
    QkButtonRow {
        z: 10
        QkButton {
            id: btn_reset
            anchors {
                horizontalCenter: parent.horizontalCenter
                verticalCenter: parent.verticalCenter
            }
            text: "重置画面"
            onClicked: {
                root.scale = 1
                root.x = 0
                root.y = 0
            }
        }

        QkButton {
            id: btn_save
            visible: false
            anchors {
                verticalCenter: parent.verticalCenter
                left: btn_reset.right
                leftMargin: 20
            }
            text: "保存"
            onClicked: {
                if (ldManage.checkLDMain(control.fileKey)) {
                    console.log("saveFile", ldManage.saveFile(control.fileKey))
                } else {
                    messageDialog.show(
                                "完整性检查失败,请满足以下条件：\n1.每个输出分支有且只有一个线圈。\n2.线圈为结束，后面不再有任何块。")
                }
                getDataBind()
            }
        }

        QkButton {
            id: btn_showlevelinfo
            anchors {
                verticalCenter: parent.verticalCenter
                left: btn_save.right
                leftMargin: 20
            }
            text: "层级编号"
            onClicked: {
                config.isShowLevelInfo = !config.isShowLevelInfo
            }
        }
        QkButton {
            anchors {
                verticalCenter: parent.verticalCenter
                left: btn_showlevelinfo.right
                leftMargin: 20
            }
            text: "引脚标签"
            onClicked: {
                config.isShowLabelName = !config.isShowLabelName
            }
        }
    }

    //画布拖动缩放
    MouseArea {
        anchors.fill: parent
        drag.target: root
        drag.axis: Drag.XAndYAxis
        drag.maximumX: 10000
        drag.minimumX: -10000
        drag.minimumY: -10000
        drag.maximumY: 10000
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        onClicked: {
            if (mouse.button === Qt.RightButton) {
                control.showMenu(mouse.x, mouse.y, "Main")
            }
        }
        onWheel: {
            if (wheel.angleDelta.y > 0) {
                root.scale += 0.1
                if (root.scale >= 2)
                    root.scale = 2
            } else {
                root.scale -= 0.1
                if (root.scale <= 0.2)
                    root.scale = 0.2
            }
        }
    }
    //画布窗口
    Rectangle {
        id: root
        x: 0
        y: 0
        width: parent.width
        height: parent.height
        border.width: 0
        border.color: "blue"
        antialiasing: true
        smooth: true
        color: "transparent"

        //全局注释窗口
        Rectangle {
            id: mainDesc
            anchors {
                left: parent.left
                leftMargin: config.defaultMargin
                top: parent.top
                topMargin: 40
            }
            border.width: 1
            border.color: config.defaultVaiableBorderColor
            height: config.defaultDescHeight * 2
            width: control.width - config.defaultMargin * 2
            color: "#CDFAFF"
            //文本编辑
            Flickable {
                id: flickText
                anchors.fill: parent
                contentWidth: editComment.paintedWidth
                contentHeight: editComment.paintedHeight
                clip: true
                function ensureVisible(r) {
                    if (contentX >= r.x)
                        contentX = r.x
                    else if (contentX + width <= r.x + r.width)
                        contentX = r.x + r.width - width
                    if (contentY >= r.y)
                        contentY = r.y
                    else if (contentY + height <= r.y + r.height)
                        contentY = r.y + r.height - height
                }
                TextEdit {
                    id: editComment
                    width: flickText.width
                    text: control.bindData ? control.bindData.Comment : ""
                    font.pixelSize: config.fontPixelSize
                    color: "black"
                    textFormat: TextEdit.PlainText
                    wrapMode: TextEdit.Wrap
                    onEditingFinished: {
                        control.bindData.Comment = text
                        var re = ldManage.modifyLDMain(control.fileKey,
                                                       bindData.Name,
                                                       bindData.Version,
                                                       bindData.Author,
                                                       bindData.Comment,
                                                       bindData.Task)
                        console.log("LDMain Commit ", re)
                    }
                    onCursorRectangleChanged: flickText.ensureVisible(
                                                  cursorRectangle)
                }
            }
        }

        Column {
            anchors {
                left: mainDesc.left
                leftMargin: 0
                top: mainDesc.bottom
                topMargin: config.defaultMargin / 2
            }
            spacing: 0
            Repeater {
                model: networkListModel
                LDNetwork {
                    id: network
                    mainControl: control
                    bindData: model
                }
            }
        }
    }

    ListModel {
        id: networkListModel
    }

    Component.onCompleted: {
        getDataBind()
        t_getValue.start()
    }

    Connections {
        target: ldManage
        function onDataChanged() {
            console.log("onDataChanged")
            getDataBind()
        }
    }

    function getDataBind() {
        control.selectID = ""
        //获取Page数据
        control.bindData = ldManage.getFileData(control.fileKey)
        //console.log("getDataBind", JSON.stringify(control.bindData))
        networkListModel.clear()
        for (var i = 0; i < control.bindData.Networks.length; i++) {
            var nw = control.bindData.Networks[i]
            //console.log("绑定网络", nw.SortNumber)
            networkListModel.append({
                                        "BlockCount": nw.BlockCount,
                                        "SortNumber": nw.SortNumber,
                                        "LabelName": nw.LabelName,
                                        "Comment": nw.Comment,
                                        "LevelInfo": nw.LevelInfo,
                                        "Blocks": []
                                    })
            //获取到新对象
            var newobj = networkListModel.get(networkListModel.count - 1)
            //添加block对象
            for (var l = 0; l < nw.Blocks.length; l++) {
                var bl = nw.Blocks[l]
                bindBlock(newobj.Blocks, bl)
            }
        }
    }

    //递归实现块数据的绑定
    function bindBlock(fathorblocks, bldata) {
        //console.log("绑定块", bldata.SortNumber, JSON.stringify(bldata))
        fathorblocks.append({
                                "BlockType": bldata.BlockType,
                                "Comment": bldata.Comment,
                                "InstanceName": bldata.InstanceName,
                                "Negate": bldata.Negate,
                                "SortNumber": bldata.SortNumber,
                                "LevelInfo": bldata.LevelInfo,
                                "condPinNum": bldata.condPinNum,
                                "inputPinNum": bldata.inputPinNum,
                                "outputPinNum": bldata.outputPinNum,
                                "paramPinNum": bldata.paramPinNum,
                                "condPinNum": bldata.condPinNum,
                                "Blocks": [],
                                "Connectors": []
                            })
        //获取到新添加的块
        var newBlock = fathorblocks.get(fathorblocks.count - 1)

        //添加引脚
        for (var k = 0; k < bldata.Connectors.length; k++) {
            var co = bldata.Connectors[k]
            newBlock.Connectors.append({
                                           "Type": co.Type,
                                           "PinId": co.PinId,
                                           "Name": co.Name,
                                           "DataType": co.DataType,
                                           "Negate": co.Negate,
                                           "LabelName": co.LabelName,
                                           "VariableID": co.VariableID,
                                           "InitValue": co.InitValue,
                                           "Value": co.Value,
                                           "Comment": co.Comment,
                                           "XOffset": co.XOffset,
                                           "YOffset": co.YOffset
                                       })
            //console.log("绑定引脚", co.PinId, newBlock.Connectors.count)
        }

        //添加子块
        for (var m = 0; m < bldata.Blocks.length; m++) {
            var cbl = bldata.Blocks[m]
            bindBlock(newBlock.Blocks, cbl)
        }
    }
    //每秒刷新获取当前值
    Timer {
        id: t_getValue
        repeat: true
        interval: 2000
        onTriggered: {

            //control.isOnline = !control.isOnline

            //从后台获取数据

            //遍历listModel修改相应的值
        }
    }

    QkMenu {
        // 右键菜单
        id: networkMenu
        property string type: ""
        width: 170
        QkMenuItem {
            enabled: checkRightMenu("Main,Network", networkMenu.type)
            text: qsTr("Insert Network")
            onTriggered: {
                var re = ldManage.insertNetwork(control.fileKey)
                console.log("Insert Network", re)
                if (re === "") {

                    //getDataBind()
                } else {
                    messageDialog.show(re)
                }
            }
        }
        QkMenuItem {
            enabled: checkRightMenu("Network", networkMenu.type)
            text: qsTr("Delete Network")
            onTriggered: {
                var re = ldManage.deleteNetwork(control.fileKey,
                                                control.selectID)
                console.log(re)
                if (re === "") {

                    //getDataBind()
                } else {
                    messageDialog.show(re)
                }
            }
        }
        QkMenuSeparator {}
        QkMenuItem {
            text: qsTr("Insert Sequence(AND)")
            enabled: checkRightMenu("Network", networkMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.insertAnd(control.fileKey,
                                                control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {

                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Insert Concurrent(OR)")
            enabled: checkRightMenu("Network", networkMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.insertOr(control.fileKey,
                                               control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
    }

    QkMenu {
        // 右键菜单
        id: contentMenu
        property string type: ""
        property int pinid: 0
        width: 170
        QkMenuItem {
            text: qsTr("Link Variable") + "..."
            enabled: checkRightMenu("IN,OUT,COND,PARAM", contentMenu.type)
            onTriggered: {
                showVariableInsertDialog()
            }
        }
        QkMenuItem {
            text: qsTr("Negate")
            enabled: checkRightMenu("CONTACT,COIL", contentMenu.type)
            onTriggered: {
                if (ldManage.negateBlock(control.fileKey, control.selectID)) {

                    //getDataBind()
                } else {
                    console.log("块取反失败")
                }
            }
        }
        QkMenuSeparator {}
        QkMenuItem {
            text: qsTr("Append FUN/FB") + "..."
            enabled: checkRightMenu("CONTACT,FUNCTION,FUNCTIONBLOCK",
                                    contentMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    showFunctionBlockInsertDialog("Append")
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Append Sequence(AND)")
            enabled: checkRightMenu("CONTACT,FUNCTION,FUNCTIONBLOCK",
                                    contentMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.appendAnd(control.fileKey,
                                                control.selectID)
                    console.log(re)
                    if (re === "") {

                        // getDataBind()
                    } else {

                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Append Concurrent(OR)")
            enabled: checkRightMenu("CONTACT,FUNCTION,FUNCTIONBLOCK",
                                    contentMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.appendOr(control.fileKey,
                                               control.selectID)
                    console.log(re)
                    if (re === "") {

                        // getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Append Contact")
            enabled: checkRightMenu("CONTACT,FUNCTION,FUNCTIONBLOCK",
                                    contentMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.appendContact(control.fileKey,
                                                    control.selectID)
                    console.log(re)
                    if (re === "") {

                        // getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuSeparator {}
        QkMenuItem {
            text: qsTr("Append Coil")
            enabled: checkRightMenu("CONTACT,FUNCTION,FUNCTIONBLOCK",
                                    contentMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.appendCoil(control.fileKey,
                                                 control.selectID)
                    console.log(re)
                    if (re === "") {

                        // getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Append JMP")
            enabled: checkRightMenu("CONTACT,FUNCTION,FUNCTIONBLOCK",
                                    contentMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.appendJMP(control.fileKey,
                                                control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Append Reset")
            enabled: checkRightMenu("CONTACT,FUNCTION,FUNCTIONBLOCK",
                                    contentMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.appendSet0(control.fileKey,
                                                 control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Append Set")
            enabled: checkRightMenu("CONTACT,FUNCTION,FUNCTIONBLOCK",
                                    contentMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.appendSet1(control.fileKey,
                                                 control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Append RET")
            enabled: checkRightMenu("CONTACT,FUNCTION,FUNCTIONBLOCK",
                                    contentMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.appendRETURN(control.fileKey,
                                                   control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuSeparator {}
        QkMenuItem {
            text: qsTr("Delete Block")
            enabled: checkRightMenu(
                         "CONTACT,FUNCTION,FUNCTIONBLOCK,COIL,JMP,Set1,Set0,RETURN",
                         contentMenu.type)
            onTriggered: {
                var re = ldManage.deleteBlock(control.fileKey, control.selectID)
                console.log(re)
                if (re === "") {

                    //getDataBind()
                } else {
                    messageDialog.show(re)
                }
            }
        }
    }

    QkMenu {
        // 容器块的右键菜单
        id: containerBlockMenu
        property string type: ""
        width: 170
        QkMenuItem {
            text: qsTr("Append FUN/FB") + "..."
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    showFunctionBlockInsertDialog("Append")
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Append Sequence(AND)")
            enabled: checkRightMenu("OR", containerBlockMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.appendAnd(control.fileKey,
                                                control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {

                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Append Concurrent(OR)")
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.appendOr(control.fileKey,
                                               control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Append Contact")
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.appendContact(control.fileKey,
                                                    control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Append Coil")
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.appendCoil(control.fileKey,
                                                 control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuSeparator {}
        QkMenuItem {
            text: qsTr("Insert FUN/FB") + "..."
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    showFunctionBlockInsertDialog("Insert")
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Insert Sequence(AND)")
            enabled: checkRightMenu("OR", containerBlockMenu.type)
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.insertAnd(control.fileKey,
                                                control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {

                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Insert Contact")
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.insertContact(control.fileKey,
                                                    control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuSeparator {}
        QkMenuItem {
            text: qsTr("Insert Coil")
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.insertCoil(control.fileKey,
                                                 control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Insert JMP")
            enabled: false
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.insertJMP(control.fileKey,
                                                control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Insert Reset")
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.insertSet0(control.fileKey,
                                                 control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Insert Set")
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.insertSet1(control.fileKey,
                                                 control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuItem {
            text: qsTr("Insert RET")
            onTriggered: {
                if (checkMaxBlockCount(control.selectID)) {
                    var re = ldManage.insertRETURN(control.fileKey,
                                                   control.selectID)
                    console.log(re)
                    if (re === "") {

                        //getDataBind()
                    } else {
                        messageDialog.show(re)
                    }
                } else {
                    messageDialog.show("本段落的块数量已超上限,无法再添加新块!")
                }
            }
        }
        QkMenuSeparator {}
        QkMenuItem {
            text: qsTr("Delete Block")
            onTriggered: {
                var re = ldManage.deleteBlock(control.fileKey, control.selectID)
                console.log(re)
                if (re === "") {

                    //getDataBind()
                } else {
                    messageDialog.show(re)
                }
            }
        }
    }

    QkPopupDialog {
        id: popupDialog
    }

    MessageDialog {
        id: messageDialog
        property var accepted
        title: qsTr("Notification")
        standardButtons: StandardButton.Ok
        function show(caption) {
            console.log("MessageDialog", caption)
            messageDialog.accepted = false
            messageDialog.text = caption
            messageDialog.open()
        }
    }

    //右键菜单显示
    function showMenu(x, y, type, pinid) {
        console.log("right click", selectID, type, pinid)
        if (type === "Main" || type === "Network") {
            contentMenu.close()
            containerBlockMenu.close()
            networkMenu.x = x
            networkMenu.y = y
            networkMenu.type = type
            networkMenu.open()
        } else if (type === "AND" || type === "OR") {
            contentMenu.close()
            networkMenu.close()
            containerBlockMenu.x = x
            containerBlockMenu.y = y
            containerBlockMenu.type = type
            containerBlockMenu.open()
        } else {
            networkMenu.close()
            containerBlockMenu.close()
            contentMenu.x = x
            contentMenu.y = y
            contentMenu.type = type
            contentMenu.pinid = pinid ? pinid : 0
            contentMenu.open()
        }
    }
    //是否具备右键菜单
    function checkRightMenu(cond, type) {
        if (cond.indexOf(type) > -1)
            return true

        return false
    }
    //变量添加面板
    function showVariableInsertDialog() {
        var c_variableInsertDialog = Qt.createComponent(
                    "qrc:/qml/LDEdit/LDVariableInsertDialog.qml")
        if (c_variableInsertDialog.status === Component.Ready) {
            control.showPopupDialog(c_variableInsertDialog)

            //popupDialog.loadercenter.item.currentLDFile = control.fileKey
            //popupDialog.loadercenter.item.deviceName = deviceName
            //popupDialog.loadercenter.item.dataType = dataType
            //popupDialog.loadercenter.item.blockData = blockData
            popupDialog.loadercenter.item.init(control.fileKey,
                                               control.selectID,
                                               control.deviceName,
                                               contentMenu.pinid)
        } else {
            console.log("LDVariableInsertDialog.qml error")
        }
    }
    //功能添加面板
    //    function showFunctionInsertDialog() {
    //        var c_functionInsertDialog = Qt.createComponent(
    //                    "qrc:/qml/LDEdit/LDFunctionInsertDialog.qml")
    //        if (c_functionInsertDialog.status === Component.Ready) {
    //            control.showPopupDialog(c_functionInsertDialog)
    //            popupDialog.loadercenter.item.init(control.fileKey)
    //        }
    //    }
    //功能块添加面板
    function showFunctionBlockInsertDialog(addtype) {
        var c_functionBlockInsertDialog = Qt.createComponent(
                    "qrc:/qml/LDEdit/LDFunctionBlockInsertDialog.qml")
        if (c_functionBlockInsertDialog.status === Component.Ready) {
            control.showPopupDialog(c_functionBlockInsertDialog)
            popupDialog.loadercenter.item.init(control.fileKey,
                                               control.selectID, addtype)
            popupDialog.loadercenter.item.insertOK.connect(function () {
                console.log("OK")
                getDataBind()
            })
        }
    }

    //交互弹窗
    function showPopupDialog(raiseItem) {
        popupDialog.raiseItem = raiseItem
        popupDialog.open()
    }

    function checkMaxBlockCount(levelinfo) {
        for (var i = 0; i < networkListModel.count; i++) {
            if (levelinfo.startsWith(networkListModel.get(i).LevelInfo)) {
                if (networkListModel.get(
                            i).BlockCount < control.maxBlockCount) {
                    return true
                }
                break
            }
        }
        return false
    }

    function savefile() {
        console.log("saveFile", ldManage.saveFile(control.fileKey))
        control.getDataBind()
    }
}
