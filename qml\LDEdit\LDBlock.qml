﻿import QtQuick 2.15

//LD块
Rectangle {
    id: control

    //主控件
    property var mainControl
    //配置
    property var config: LDEditorConfig
    //绑定的数据
    //类型 AND OR CONTACT COIL JMP Set1 Set0 RETURN FUNCTION FUNCTIONBLOCK
    property var bindData

    width: loader.item.width
    height: loader.item.height + config.defaultDescHeight

    //每个块的边框
    color: "transparent"
    border {
        width: 1
        color: mainControl.selectID
               === bindData.LevelInfo ? config.defaultVaiableBorderColor : "transparent"
    }

    MouseArea {
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        drag.target: mainControl.mainroot
        drag.axis: Drag.XAndYAxis
        drag.maximumX: 10000
        drag.minimumX: -10000
        drag.minimumY: -10000
        drag.maximumY: 10000
        onClicked: {
            mainControl.selectID = bindData.LevelInfo
            if (mouse.button === Qt.RightButton) {
                var positionInRoot = mapToItem(mainControl, mouse.x, mouse.y)
                mainControl.showMenu(positionInRoot.x, positionInRoot.y,
                                     bindData.BlockType)
            }
        }
        onWheel: {
            if (wheel.angleDelta.y > 0) {
                root.scale += 0.1
                if (root.scale >= 2)
                    root.scale = 2
            } else {
                root.scale -= 0.1
                if (root.scale <= 0.2)
                    root.scale = 0.2
            }
        }
    }

    //动态加载块的实体
    Loader {
        id: loader
        source: getTypeComponent(control.bindData.BlockType)
        onLoaded: {
            loader.item.bindData = control.bindData
            loader.item.mainControl = control.mainControl
        }
    }

    //注释配置位
    Rectangle {
        id: desc
        visible: showComment(control.bindData.BlockType)
        anchors {
            left: loader.left
            leftMargin: 5
            top: loader.bottom
            topMargin: 5
        }
        height: config.defaultDescHeight
        width: control.width - 10
        color: config.defaultBlockDescBackgroundColor
        //文本编辑
        Flickable {
            id: flickText
            anchors.fill: parent
            contentWidth: editComment.paintedWidth
            contentHeight: editComment.paintedHeight
            clip: true
            function ensureVisible(r) {
                if (contentX >= r.x)
                    contentX = r.x
                else if (contentX + width <= r.x + r.width)
                    contentX = r.x + r.width - width
                if (contentY >= r.y)
                    contentY = r.y
                else if (contentY + height <= r.y + r.height)
                    contentY = r.y + r.height - height
            }
            TextEdit {
                id: editComment
                width: flickText.width
                text: control.bindData ? control.bindData.Comment: ""
                font.pixelSize: config.fontPixelSize
                color: "#097B44"
                textFormat: TextEdit.PlainText
                wrapMode: TextEdit.Wrap
                onCursorRectangleChanged: flickText.ensureVisible(
                                              cursorRectangle)
                onEditingFinished: {
                    bindData.Comment = text
                    var re = ldManage.modifyBlock(
                                mainControl.fileKey,
                                bindData.LevelInfo, bindData.Negate,
                                bindData.Comment)

                    console.log("LDBlock Comment update", re)
                }
            }
        }
    }
    //层级编号显示
    Text {
        anchors {
            horizontalCenter: parent.horizontalCenter
            bottom: parent.bottom
        }
        font.pixelSize: config.fontPixelSize
        visible: config.isShowLevelInfo
        text: bindData.LevelInfo
    }

    function getTypeComponent(type) {
        //console.log("getTypeComponent", type)
        if (type === "END")
            return "LD_END.qml"
        if (type === "AND")
            return "LD_AND.qml"
        if (type === "OR")
            return "LD_OR.qml"
        if (type === "CONTACT")
            return "LD_CONTACT.qml"
        if (type === "COIL")
            return "LD_COIL.qml"
        if (type === "JMP")
            return "LD_JMP.qml"
        if (type === "Set1")
            return "LD_Set1.qml"
        if (type === "Set0")
            return "LD_Set0.qml"
        if (type === "RETURN")
            return "LD_RETURN.qml"
        if (type === "FUNCTION")
            return "LD_FUNCTION.qml"
        if (type === "FUNCTIONBLOCK")
            return "LD_FUNCTIONBLOCK.qml"
    }

    //根据类型显示注释
    function showComment(type) {
        if (type === "AND" || type === "OR" || type === "END")
            return false

        return true
    }
}
