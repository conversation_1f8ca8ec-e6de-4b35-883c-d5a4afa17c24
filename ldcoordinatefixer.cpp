﻿#include <QQueue>
#include <QSet>
#include <QMap>
#include <QPair>
#include <QStack>
#include <algorithm>
#include <limits>
#include "hlog.h"
#include "ldcoordinatefixer.h"
#include "ldmanager.h"


// Connection关系只分两种，1水平串联 2.其它串联
// 水平串联：当两个元件存在Connection时，且SourceConnectIndex = 0, TargetConnectIndex = 0，则源元件和输出元件是水平串联,坐标关系为（x, y）和（x + 1, y），否则就是其它串联
// 如果多个元件的源元件是同一个元件可以认为这多个元件是并联结构，根据源元件的SourceConnectIndex来决定并联结构中元件的坐标关系，从源元件的SourceConnectIndex = 0引脚开始，依次调整元件的Y坐标
// 如果多个元件的输出元件是同一个元件可以认为这多个元件是并联结构，根据输出元件的TargetConnectIndex来决定并联结构中元件的坐标关系，从输出元件的TargetConnectIndex = 0引脚开始，依次调整元件的Y坐标
// 修复元件坐标
bool LDCoordinateFixer::fixComponentCoordinates(LDFile* ld, int networkNumber)
{

    if (!ld || !ld->networks || !ld->components || !ld->connections)
    {
        LOG_ERROR_DEFAULT("空指针: ld或其成员为空");
        return false;
    }

    // 检查网络是否存在
    if (!ld->networks->networkMap.contains(networkNumber))
    {
        LOG_ERROR_DEFAULT("网络不存在: networkNumber={}", networkNumber);
        return false;
    }

    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);
    if (!network)
    {
        LOG_ERROR_DEFAULT("网络指针为空: networkNumber={}", networkNumber);
        return false;
    }

    LOG_INFO_DEFAULT("开始修复{}网络元件坐标", network->Number);

    // 获取START元件
    QSharedPointer<LDComponent> startComponent = getStartComponent(ld, networkNumber);
    if (!startComponent)
    {
        LOG_ERROR_DEFAULT("未找到START元件");
        return false;
    }

    // 初始化遍历状态
    QStack<int> traversalStack;
    QMap<int, QPair<int, int>> componentCoordinates;
    QMap<QPair<int, int>, int> occupiedPositions;
    QSet<int> visited;
    QSet<int> orBlocks;

    // 设置START元件坐标为(0,0)
    startComponent->XPos = 0;
    startComponent->YPos = 0;
    componentCoordinates[startComponent->Number] = QPair<int, int>(0, 0);
    occupiedPositions[QPair<int, int>(0, 0)] = startComponent->Number;


    bool success = preciseTraverseAndFix(ld, startComponent->Number,
        traversalStack, componentCoordinates, occupiedPositions,
        visited, orBlocks, networkNumber);

    if (success)
    {
        // 应用坐标到所有元件
        applyCoordinatesToComponents(ld, componentCoordinates);
        LOG_INFO_DEFAULT("坐标修复完成: networkNumber={}", networkNumber);
    }
    else
    {
        LOG_ERROR_DEFAULT("坐标修复失败: networkNumber={}", networkNumber);
    }
    setParentComponent(ld, networkNumber);
    refreshTaskOrderNumber(ld, networkNumber, startComponent);

    return success;
}

// 获取元件的所有连接信息
LDCoordinateFixer::ConnectionInfo LDCoordinateFixer::getComponentConnections(LDFile* ld, int componentNumber)
{
    ConnectionInfo connInfo;

    // 用于存储分支连接信息，便于排序
    struct BranchInfo
    {
        int targetNumber;
        int sourceConnectIndex;
        int targetConnectIndex;
    };
    QList<BranchInfo> branchList;

    for (auto& connection : ld->connections->connectionList)
    {
        if (connection->SourceComponentNumber == componentNumber)
        {
            // 跳过变量元件连接
            QSharedPointer<LDComponent> targetComponent = ld->components->componentMap.value(connection->TargetComponentNumber);
            if (targetComponent && targetComponent->Type == "Variable")
            {
                continue;
            }

            // 水平串联条件
            if (connection->SourceConnectIndex == 0 && connection->TargetConnectIndex == 0)
            {
                connInfo.horizontalTargets.append(connection->TargetComponentNumber);
                LOG_INFO_DEFAULT("元件{}的水平串联目标: {}", componentNumber, connection->TargetComponentNumber);
            }
            else
            {
                // 其他连接视为分支，先收集到列表中
                BranchInfo branch;
                branch.targetNumber = connection->TargetComponentNumber;
                branch.sourceConnectIndex = connection->SourceConnectIndex;
                branch.targetConnectIndex = connection->TargetConnectIndex;
                branchList.append(branch);
            }
        }
    }

    // 按照SourceConnectIndex从小到大排序
    std::sort(branchList.begin(), branchList.end(),
        [](const BranchInfo& a, const BranchInfo& b)
        {
            return a.sourceConnectIndex < b.sourceConnectIndex;
        });

    // 将排序后的分支添加到connInfo
    for (const BranchInfo& branch : branchList)
    {
        connInfo.branchTargets.append(branch.targetNumber);
        LOG_INFO_DEFAULT("元件{}的分支目标元件: {} (SourceConnectIndex={}, TargetConnectIndex={})",
            componentNumber, branch.targetNumber,
            branch.sourceConnectIndex, branch.targetConnectIndex);
    }

    return connInfo;
}

// 应用坐标到元件
void LDCoordinateFixer::applyCoordinatesToComponents(LDFile* ld,
    const QMap<int, QPair<int, int>>& componentCoordinates)
{
    LOG_INFO_DEFAULT("开始应用坐标到元件，共{}个元件", componentCoordinates.size());

    for (auto it = componentCoordinates.constBegin(); it != componentCoordinates.constEnd(); ++it)
    {
        int componentNumber = it.key();
        QPair<int, int> coordinates = it.value();

        if (ld->components->componentMap.contains(componentNumber))
        {
            QSharedPointer<LDComponent> component = ld->components->componentMap.value(componentNumber);
            if (component)
            {
                // 跳过变量元件
                if (component->Type == "Variable")
                {
                    continue;
                }

                int oldX = component->XPos;
                int oldY = component->YPos;

                component->XPos = coordinates.first;
                component->YPos = coordinates.second;

                LOG_INFO_DEFAULT("元件{}坐标更新: ({},{}) -> ({},{})",
                    componentNumber, oldX, oldY, coordinates.first, coordinates.second);
            }
        }
        else
        {
            LOG_ERROR_DEFAULT("元件{}不存在，无法应用坐标({},{})",
                componentNumber, coordinates.first, coordinates.second);
        }
    }

    LOG_INFO_DEFAULT("坐标应用完成");
}

// 寻找下一个可用的Y坐标
int LDCoordinateFixer::findNextAvailableY(int baseX, int startY,
    const QMap<QPair<int, int>, int>& occupiedPositions)
{
    int currentY = startY;

    // 从startY开始向下搜索空闲位置
    while (occupiedPositions.contains(QPair<int, int>(baseX, currentY)))
    {
        currentY++;
        // 防止无限循环，设置最大搜索范围
        if (currentY > startY + 100)
        {
            LOG_ERROR_DEFAULT("寻找可用Y坐标超出范围: baseX={}, startY={}", baseX, startY);
            break;
        }
    }

    LOG_INFO_DEFAULT("为坐标({},{})找到可用Y坐标: {}", baseX, startY, currentY);
    return currentY;
}

// 获取start元件
QSharedPointer<LDComponent> LDCoordinateFixer::getStartComponent(LDFile* ld, int networkNumber)
{
    // 获取网络
    QSharedPointer<LDNetwork> network = ld->networks->networkMap.value(networkNumber);
    if (!network)
    {
        LOG_ERROR_DEFAULT("网络不存在: networkNumber={}", networkNumber);
        return nullptr;
    }
    // 获取网络的START元件
    // QSharedPointer<LDComponent> startComponent = network->StartComponent;
    for (auto& component : network->componentMap)
    {
        if (component && component->Type == "Variable" &&
            component->ChildType == "Constant" &&
            component->AuxContent == "???" &&
            component->AuxContent1 == "Start")
        {
            return component;
        }
    }
    return nullptr;
}


// 获取元件的最大输入路径长度
int LDCoordinateFixer::getMaxInputPathLength(LDFile* ld, int componentNumber, QSet<int>& visited)
{
    if (visited.contains(componentNumber))
    {
        return 0; // 防止循环
    }

    visited.insert(componentNumber);

    // 找到所有连接到该元件的输入元件
    QList<int> inputComponents;
    for (auto& connection : ld->connections->connectionList)
    {
        if (connection->TargetComponentNumber == componentNumber)
        {
            inputComponents.append(connection->SourceComponentNumber);
        }
    }

    if (inputComponents.isEmpty())
    {
        return 0; // 没有输入，返回0
    }

    // 计算每个输入路径的长度，取最大值
    int maxLength = 0;
    for (int sourceNumber : inputComponents)
    {
        if (!ld->components->componentMap.contains(sourceNumber))
        {
            continue;
        }

        QSharedPointer<LDComponent> sourceComponent = ld->components->componentMap.value(sourceNumber);
        if (!sourceComponent)
        {
            continue;
        }

        // 对于START元件，路径长度为1
        if (sourceComponent->Type == "Variable" &&
            sourceComponent->ChildType == "Constant" &&
            sourceComponent->AuxContent == "???" &&
            sourceComponent->AuxContent1 == "Start")
        {
            maxLength = qMax(maxLength, 1);
            continue;
        }

        // 递归计算输入元件的最大路径长度
        QSet<int> subVisited = visited; // 创建副本以避免影响其他分支
        int subLength = this->getMaxInputPathLength(ld, sourceNumber, subVisited);
        maxLength = qMax(maxLength, subLength + 1); // +1表示当前连接
    }

    return maxLength;
}

// 分配分支坐标
bool LDCoordinateFixer::allocateBranchCoordinates(LDFile* ld, int sourceComponentNumber,
    const QList<int>& branchComponents,
    QMap<int, QPair<int, int>>& componentCoordinates,
    QMap<QPair<int, int>, int>& occupiedPositions,
    QSet<int>& orBlocks, int networkNumber)
{
    if (branchComponents.isEmpty())
    {
        return true;
    }

    QSharedPointer<LDComponent> sourceComponent = ld->components->componentMap.value(sourceComponentNumber);
    if (!sourceComponent)
    {
        LOG_ERROR_DEFAULT("源元件不存在: {}", sourceComponentNumber);
        return false;
    }

    LOG_INFO_DEFAULT("为源元件{}分配{}个分支坐标", sourceComponentNumber, branchComponents.size());

    int baseX = sourceComponent->XPos;
    int baseY = sourceComponent->YPos;

    // 为每个分支元件分配坐标，通过模拟路径预测坐标需求
    for (int i = 0; i < branchComponents.size(); ++i)
    {
        int branchComponentNumber = branchComponents[i];
        QSharedPointer<LDComponent> branchComponent = ld->components->componentMap.value(branchComponentNumber);

        if (!branchComponent)
        {
            LOG_ERROR_DEFAULT("分支元件不存在: {}", branchComponentNumber);
            continue;
        }

        // 模拟从当前分支元件开始的路径，预测所需的坐标空间
        QMap<int, QPair<int, int>> simulatedCoordinates;
        QMap<QPair<int, int>, int> simulatedOccupied = occupiedPositions; // 复制当前占用状态

        // 预测这个分支的坐标需求
        int predictedX = baseX;
        int predictedY = baseY;

        // 特殊处理OR块 - 需要计算正确的X坐标
        if (branchComponent->Type == "Block" &&
            branchComponent->ChildType == "Func" &&
            branchComponent->AuxContent == "Or")
        {
            if (!orBlocks.contains(branchComponentNumber))
            {
                orBlocks.insert(branchComponentNumber);
            }

            // OR块需要根据输入路径长度调整X坐标
            QSet<int> pathVisited;
            int maxInputPathLength = getMaxInputPathLength(ld, branchComponentNumber, pathVisited);
            predictedX = maxInputPathLength;

            LOG_INFO_DEFAULT("OR块{}预测X坐标: {} -> {}", branchComponentNumber, baseX, predictedX);
        }

        // 模拟这个分支可能产生的后续路径
        QList<int> pathComponents = simulateBranchPath(ld, branchComponentNumber);
        LOG_INFO_DEFAULT("模拟分支{}路径，包含{}个元件", branchComponentNumber, pathComponents.size());

        // 寻找能容纳整个路径的起始Y坐标
        int startY = findSafeStartingY(predictedX, baseY + 1, pathComponents.size(),
            simulatedOccupied);

        // 分配坐标给这个分支的首元件
        predictedY = startY;

        // 设置元件坐标
        branchComponent->XPos = predictedX;
        branchComponent->YPos = predictedY;
        componentCoordinates[branchComponentNumber] = QPair<int, int>(predictedX, predictedY);
        occupiedPositions[QPair<int, int>(predictedX, predictedY)] = branchComponentNumber;

        LOG_INFO_DEFAULT("设置分支元件{}坐标: ({},{}) [路径模拟完成]",
            branchComponentNumber, predictedX, predictedY);
    }

    return true;
}

// 新增：模拟分支路径，返回这个分支可能包含的所有元件
QList<int> LDCoordinateFixer::simulateBranchPath(LDFile* ld, int startComponentNumber)
{
    QList<int> pathComponents;
    QSet<int> visited;

    // 递归模拟路径
    simulateBranchPathRecursive(ld, startComponentNumber, pathComponents, visited);

    return pathComponents;
}

// 新增：递归模拟分支路径
void LDCoordinateFixer::simulateBranchPathRecursive(LDFile* ld, int componentNumber,
    QList<int>& pathComponents,
    QSet<int>& visited)
{
    if (visited.contains(componentNumber))
    {
        return; // 防止循环
    }

    visited.insert(componentNumber);
    pathComponents.append(componentNumber);

    // 查找这个元件的所有输出连接
    for (auto& connection : ld->connections->connectionList)
    {
        if (connection->SourceComponentNumber == componentNumber)
        {
            QSharedPointer<LDComponent> targetComponent = ld->components->componentMap.value(connection->TargetComponentNumber);
            if (targetComponent && targetComponent->Type != "Variable")
            {
                simulateBranchPathRecursive(ld, connection->TargetComponentNumber, pathComponents, visited);
            }
        }
    }
}

// 寻找能安全容纳整个路径的起始Y坐标
int LDCoordinateFixer::findSafeStartingY(int baseX, int preferredStartY, int pathLength,
    const QMap<QPair<int, int>, int>& occupiedPositions)
{
    int currentY = preferredStartY;

    // 检查从currentY开始的连续pathLength行是否有足够空间
    while (true)
    {
        bool hasConflict = false;

        // 检查从currentY开始的多行是否被占用
        for (int offset = 0; offset < qMax(pathLength, 1); ++offset)
        {
            // 只检查当前X坐标位置，不检查水平扩展
            QPair<int, int> checkPos(baseX, currentY + offset);
            if (occupiedPositions.contains(checkPos))
            {
                hasConflict = true;
                LOG_INFO_DEFAULT("检测到坐标冲突: ({},{}) 被元件{}占用",
                    checkPos.first, checkPos.second, occupiedPositions[checkPos]);
                break;
            }
        }

        if (!hasConflict)
        {
            LOG_INFO_DEFAULT("为路径长度{}找到安全起始Y坐标: {} (baseX={})",
                pathLength, currentY, baseX);
            return currentY;
        }

        currentY++;

        // 防止无限循环
        if (currentY > preferredStartY + 50)
        {
            LOG_ERROR_DEFAULT("寻找安全起始Y坐标超出范围: baseX={}, preferredStartY={}, pathLength={}",
                baseX, preferredStartY, pathLength);
            return preferredStartY;
        }
    }
}

// 首先处理水平串联, 处理到达末端后的回溯分支
bool LDCoordinateFixer::preciseTraverseAndFix(LDFile* ld, int componentNumber,
    QStack<int>& traversalStack,
    QMap<int, QPair<int, int>>& componentCoordinates,
    QMap<QPair<int, int>, int>& occupiedPositions,
    QSet<int>& visited, QSet<int>& orBlocks,
    int networkNumber)
{

    visited.insert(componentNumber);
    traversalStack.push(componentNumber);

    // LOG_INFO_DEFAULT("访问元件: {}, 栈深度: {}", componentNumber, traversalStack.size());

    // 获取当前元件
    QSharedPointer<LDComponent> currentComponent = ld->components->componentMap.value(componentNumber);
    if (!currentComponent)
    {
        LOG_ERROR_DEFAULT("当前元件不存在: {}", componentNumber);
        return false;
    }

    // 首先处理水平串联 - 这是主干路径
    ConnectionInfo connInfo = getComponentConnections(ld, componentNumber);

    // 优先处理水平串联连接（主干）
    for (int targetNumber : connInfo.horizontalTargets)
    {
        if (!visited.contains(targetNumber))
        {
            QSharedPointer<LDComponent> targetComponent = ld->components->componentMap.value(targetNumber);
            if (!targetComponent) continue;

            // 设置水平串联坐标
            int newX = currentComponent->XPos + 1;
            int newY = currentComponent->YPos;

            // 特殊处理OR块
            if (targetComponent->Type == "Block" &&
                targetComponent->ChildType == "Func" &&
                targetComponent->AuxContent == "Or")
            {
                QSet<int> pathVisited;
                int maxInputPathLength = getMaxInputPathLength(ld, targetNumber, pathVisited);
                newX = maxInputPathLength;
                orBlocks.insert(targetNumber);
                LOG_INFO_DEFAULT("OR块{}，X坐标调整为: {}", targetNumber, newX);
            }

            targetComponent->XPos = newX;
            targetComponent->YPos = newY;
            componentCoordinates[targetNumber] = QPair<int, int>(newX, newY);
            occupiedPositions[QPair<int, int>(newX, newY)] = targetNumber;

            LOG_INFO_DEFAULT("水平串联: {}({},{}) -> {}({},{})",
                componentNumber, currentComponent->XPos, currentComponent->YPos,
                targetNumber, newX, newY);

            // 递归处理水平串联
            if (!preciseTraverseAndFix(ld, targetNumber, traversalStack, componentCoordinates,
                occupiedPositions, visited, orBlocks, networkNumber))
            {
                return false;
            }
        }
    }

    // 处理到达末端后的回溯分支
    if (connInfo.horizontalTargets.isEmpty() ||
        std::all_of(connInfo.horizontalTargets.begin(), connInfo.horizontalTargets.end(),
            [&visited](int target) { return visited.contains(target); }))
    {
        // 到达末端，开始回溯处理分支
        LOG_INFO_DEFAULT("元件{}到达末端，开始回溯处理分支", componentNumber);

        if (!handleSpecificTraversalPattern(ld, componentNumber, traversalStack,
            componentCoordinates, occupiedPositions,
            visited, orBlocks, networkNumber))
        {
            return false;
        }
    }

    traversalStack.pop();
    LOG_INFO_DEFAULT("离开元件: {}", componentNumber);

    return true;
}

// 检查并处理特定的遍历模式
bool LDCoordinateFixer::handleSpecificTraversalPattern(LDFile* ld, int currentComponent,
    QStack<int>& traversalStack,
    QMap<int, QPair<int, int>>& componentCoordinates,
    QMap<QPair<int, int>, int>& occupiedPositions,
    QSet<int>& visited, QSet<int>& orBlocks,
    int networkNumber)
{
    QSharedPointer<LDComponent> component = ld->components->componentMap.value(currentComponent);
    if (!component) return false;

    // 查找当前元件的所有未处理分支
    ConnectionInfo connInfo = getComponentConnections(ld, currentComponent);
    QList<int> unprocessedBranches;

    for (int branchTarget : connInfo.branchTargets)
    {
        if (!visited.contains(branchTarget))
        {
            unprocessedBranches.append(branchTarget);
        }
    }

    if (!unprocessedBranches.isEmpty())
    {
        LOG_INFO_DEFAULT("元件{}处理{}个分支", currentComponent, unprocessedBranches.size());

        // 输出当前占用的坐标位置
        LOG_INFO_DEFAULT("当前已占用坐标位置:");
        for (auto it = occupiedPositions.constBegin(); it != occupiedPositions.constEnd(); ++it)
        {
            LOG_INFO_DEFAULT("坐标({},{}) 被元件{}占用", it.key().first, it.key().second, it.value());
        }

        // 为每个分支分配坐标，考虑避免冲突
        int baseX = component->XPos;
        int baseY = component->YPos;
        int maxAssignedY = baseY; // 跟踪已分配分支的最大Y坐标

        for (int i = 0; i < unprocessedBranches.size(); ++i)
        {
            int branchTarget = unprocessedBranches[i];
            QSharedPointer<LDComponent> branchComponent = ld->components->componentMap.value(branchTarget);

            if (!branchComponent) continue;

            // 分支元件的X坐标应该是源元件X+1（符合连接规则）
            int targetX = baseX + 1;
            int targetY = baseY;

            // 特殊处理OR块的X坐标
            if (branchComponent->Type == "Block" &&
                branchComponent->ChildType == "Func" &&
                branchComponent->AuxContent == "Or")
            {
                QSet<int> pathVisited;
                int maxInputPathLength = getMaxInputPathLength(ld, branchTarget, pathVisited);
                targetX = maxInputPathLength;
                orBlocks.insert(branchTarget);
                LOG_INFO_DEFAULT("分支OR块{}，X坐标调整为: {}", branchTarget, targetX);
            }

            // 模拟水平串联路径以确定Y坐标
            QList<int> horizontalPath = simulateHorizontalPath(ld, branchTarget);
            LOG_INFO_DEFAULT("模拟分支{}的水平路径，包含{}个元件", branchTarget, horizontalPath.size());

            // 初始Y坐标查找 - 确保新分支的Y坐标大于已分配的分支
            if (i == 0 && baseY == 0)
            {
                // 第一个分支且在第一行，从Y=1开始
                targetY = 1;
            }
            else
            {
                // 从已分配的最大Y+1开始，确保不会与之前的分支冲突
                targetY = maxAssignedY + 1;
            }

            // 检查水平路径是否有冲突，如果有则向下查找合适的Y坐标
            while (checkPathConflicts(horizontalPath, targetX, targetY, occupiedPositions, componentCoordinates, ld, orBlocks))
            {
                targetY++;
                if (targetY > baseY + 50) // 防止无限循环
                {
                    LOG_ERROR_DEFAULT("无法为分支{}找到合适的Y坐标", branchTarget);
                    break;
                }
            }

            LOG_INFO_DEFAULT("分支{}将放置在Y={}，水平路径长度={}", branchTarget, targetY, horizontalPath.size());

            // 为整个水平路径分配坐标
            int pathX = targetX;
            int maxPathY = targetY; // 跟踪路径中的最大Y坐标
            for (int j = 0; j < horizontalPath.size(); ++j)
            {
                int pathComponentNumber = horizontalPath[j];
                QSharedPointer<LDComponent> pathComponent = ld->components->componentMap.value(pathComponentNumber);

                if (!pathComponent) continue;

                // 特殊处理OR块
                if (pathComponent->Type == "Block" &&
                    pathComponent->ChildType == "Func" &&
                    pathComponent->AuxContent == "Or")
                {
                    QSet<int> pathVisited;
                    pathX = getMaxInputPathLength(ld, pathComponentNumber, pathVisited);
                    orBlocks.insert(pathComponentNumber);
                }

                pathComponent->XPos = pathX;
                pathComponent->YPos = targetY;
                componentCoordinates[pathComponentNumber] = QPair<int, int>(pathX, targetY);
                occupiedPositions[QPair<int, int>(pathX, targetY)] = pathComponentNumber;

                LOG_INFO_DEFAULT("设置路径元件{}坐标: ({},{})", pathComponentNumber, pathX, targetY);

                // 下一个元件X+1
                if (j < horizontalPath.size() - 1)
                {
                    pathX++;
                }
            }

            // 更新已分配的最大Y坐标
            maxAssignedY = qMax(maxAssignedY, targetY);

            LOG_INFO_DEFAULT("分支处理完成: {}({},{}) -> 分支{}({},{})，水平路径包含{}个元件",
                currentComponent, baseX, baseY, branchTarget, targetX, targetY, horizontalPath.size());

            // 递归处理该分支
            if (!preciseTraverseAndFix(ld, branchTarget, traversalStack, componentCoordinates,
                occupiedPositions, visited, orBlocks, networkNumber))
            {
                return false;
            }
        }
    }

    return true;
}

// 模拟水平串联路径（只沿着SourceConnectIndex=0, TargetConnectIndex=0的连接）
QList<int> LDCoordinateFixer::simulateHorizontalPath(LDFile* ld, int startComponentNumber)
{
    QList<int> pathComponents;
    QSet<int> visited;

    int currentComponent = startComponentNumber;

    while (currentComponent != -1)
    {
        if (visited.contains(currentComponent))
        {
            break; // 防止循环
        }

        visited.insert(currentComponent);
        pathComponents.append(currentComponent);

        // 查找水平串联的下一个元件
        int nextComponent = -1;
        for (auto& connection : ld->connections->connectionList)
        {
            // 水平串联条件：SourceConnectIndex = 0, TargetConnectIndex = 0
            if (connection->SourceComponentNumber == currentComponent &&
                connection->SourceConnectIndex == 0 && connection->TargetConnectIndex == 0)
            {
                QSharedPointer<LDComponent> targetComponent = ld->components->componentMap.value(connection->TargetComponentNumber);
                if (targetComponent && targetComponent->Type != "Variable")
                {
                    nextComponent = connection->TargetComponentNumber;
                    break;
                }
            }
        }

        currentComponent = nextComponent;
    }

    LOG_INFO_DEFAULT("水平路径模拟: 从元件{}开始，包含{}个元件", startComponentNumber, pathComponents.size());
    return pathComponents;
}

// 检查路径在指定Y坐标上是否有冲突
bool LDCoordinateFixer::checkPathConflicts(const QList<int>& pathComponents, int startX, int startY,
    const QMap<QPair<int, int>, int>& occupiedPositions,
    const QMap<int, QPair<int, int>>& componentCoordinates,
    LDFile* ld, QSet<int>& orBlocks)
{
    int currentX = startX;

    for (int i = 0; i < pathComponents.size(); ++i)
    {
        int componentNumber = pathComponents[i];
        QSharedPointer<LDComponent> component = ld->components->componentMap.value(componentNumber);

        if (!component) continue;

        // 特殊处理OR块
        if (component->Type == "Block" &&
            component->ChildType == "Func" &&
            component->AuxContent == "Or")
        {
            QSet<int> pathVisited;
            currentX = getMaxInputPathLength(ld, componentNumber, pathVisited);
        }

        QPair<int, int> checkPos(currentX, startY);

        // 检查是否已被其他元件占用
        if (occupiedPositions.contains(checkPos) &&
            occupiedPositions[checkPos] != componentNumber)
        {
            LOG_INFO_DEFAULT("路径冲突: 坐标({},{})已被元件{}占用，当前元件{}无法放置",
                currentX, startY, occupiedPositions[checkPos], componentNumber);
            return true; // 有冲突
        }

        // 检查该元件到其所有输出目标的虚拟路径
        QList<OutputTarget> outputTargets = getComponentOutputTargets(ld, componentNumber, componentCoordinates);
        for (const OutputTarget& target : outputTargets)
        {
            // 只检查分支连接（非水平串联）的虚拟路径
            if (target.TargetConnectIndex != 0) // 非水平串联输入
            {
                if (checkVirtualPathConflict(currentX, startY, target.targetX, target.targetY,
                    occupiedPositions, componentNumber, target.componentNumber))
                {
                    LOG_INFO_DEFAULT("元件{}在({},{})的虚拟路径被阻塞，无法到达目标{}({},{})",
                        componentNumber, currentX, startY,
                        target.componentNumber, target.targetX, target.targetY);
                    return true; // 有冲突
                }
            }
        }

        // 特殊处理：对于所有没有输出引脚的元件，检查虚拟路径冲突
        if (outputTargets.isEmpty())
        {
            QSharedPointer<LDComponent> comp = ld->components->componentMap.value(componentNumber);
            if (comp && comp->Type == "Block")
            {
                // 使用targetX = -1来触发无输出引脚元件的虚拟路径检测
                if (checkVirtualPathConflict(currentX, startY, -1, -1,
                    occupiedPositions, componentNumber, -1))
                {
                    LOG_INFO_DEFAULT("无输出引脚元件{}在({},{})的虚拟路径被阻塞",
                        componentNumber, currentX, startY);
                    return true; // 有冲突
                }
            }
        }

        // 检查到下一个元件之间的连线路径
        if (i < pathComponents.size() - 1)
        {
            int nextComponentNumber = pathComponents[i + 1];
            QSharedPointer<LDComponent> nextComponent = ld->components->componentMap.value(nextComponentNumber);

            if (nextComponent)
            {
                int nextX = currentX + 1;

                // 特殊处理OR块
                if (nextComponent->Type == "Block" &&
                    nextComponent->ChildType == "Func" &&
                    nextComponent->AuxContent == "Or")
                {
                    QSet<int> pathVisited;
                    nextX = getMaxInputPathLength(ld, nextComponentNumber, pathVisited);
                }

                // 检查从当前元件到下一个元件之间的所有中间点
                for (int x = currentX + 1; x < nextX; x++)
                {
                    QPair<int, int> pathPos(x, startY);
                    if (occupiedPositions.contains(pathPos) &&
                        occupiedPositions[pathPos] != componentNumber &&
                        occupiedPositions[pathPos] != nextComponentNumber)
                    {
                        LOG_INFO_DEFAULT("连线路径冲突: 从元件{}({},{})到{}的连线路径在({},{})被元件{}占用",
                            componentNumber, currentX, startY, nextComponentNumber,
                            x, startY, occupiedPositions[pathPos]);
                        return true; // 有冲突
                    }
                }
            }
        }

        // 下一个元件X坐标+1（水平串联）
        if (i < pathComponents.size() - 1)
        {
            currentX++;
        }
    }

    return false; // 无冲突
}

// 获取元件的所有输出目标及其坐标
QList<LDCoordinateFixer::OutputTarget> LDCoordinateFixer::getComponentOutputTargets(LDFile* ld, int componentNumber,
    const QMap<int, QPair<int, int>>& componentCoordinates)
{
    QList<OutputTarget> targets;

    // 获取该元件的所有输出连接
    for (auto& connection : ld->connections->connectionList)
    {
        if (connection->SourceComponentNumber == componentNumber)
        {
            QSharedPointer<LDComponent> targetComponent = ld->components->componentMap.value(connection->TargetComponentNumber);
            if (targetComponent && targetComponent->Type != "Variable")
            {
                OutputTarget target;
                target.componentNumber = connection->TargetComponentNumber;
                target.TargetConnectIndex = connection->TargetConnectIndex;

                // 获取目标元件的坐标
                if (componentCoordinates.contains(connection->TargetComponentNumber))
                {
                    QPair<int, int> coord = componentCoordinates[connection->TargetComponentNumber];
                    target.targetX = coord.first;
                    target.targetY = coord.second;
                }
                else
                {
                    // 如果坐标还未分配，使用当前坐标
                    target.targetX = targetComponent->XPos;
                    target.targetY = targetComponent->YPos;
                }

                targets.append(target);
            }
        }
    }

    return targets;
}

// 获取指定Y坐标上已占用位置的最大X坐标
int LDCoordinateFixer::getMaxXAtY(int targetY, const QMap<QPair<int, int>, int>& occupiedPositions)
{
    int maxX = -1;
    for (auto it = occupiedPositions.constBegin(); it != occupiedPositions.constEnd(); ++it)
    {
        const QPair<int, int>& pos = it.key();
        if (pos.second == targetY && pos.first > maxX)
        {
            maxX = pos.first;
        }
    }
    return maxX;
}

// 检查从源位置到目标位置的虚拟路径是否有冲突
bool LDCoordinateFixer::checkVirtualPathConflict(int sourceX, int sourceY, int targetX, int targetY,
    const QMap<QPair<int, int>, int>& occupiedPositions,
    int sourceComponent, int targetComponent)
{
    // 如果目标在源的右边且不在同一行，需要检查虚拟路径
    if (targetX > sourceX && targetY != sourceY)
    {
        // 水平移动到目标X的前一格
        for (int x = sourceX + 1; x < targetX; x++)
        {
            QPair<int, int> checkPos(x, sourceY);
            if (occupiedPositions.contains(checkPos) &&
                occupiedPositions[checkPos] != sourceComponent &&
                occupiedPositions[checkPos] != targetComponent)
            {
                LOG_INFO_DEFAULT("虚拟路径冲突: 元件{}到{}的路径在({},{})被元件{}阻塞",
                    sourceComponent, targetComponent, x, sourceY, occupiedPositions[checkPos]);
                return true; // 有冲突
            }
        }
    }

    // 特殊处理：当targetX == -1时，表示检查无输出引脚元件的虚拟路径
    // 这种情况下，检查从源位置到当前Y坐标上最大X坐标的路径
    if (targetX == -1)
    {
        int maxXAtY = getMaxXAtY(sourceY, occupiedPositions);
        if (maxXAtY > sourceX)
        {
            // 检查从源位置到最大X坐标的水平路径
            for (int x = sourceX + 1; x <= maxXAtY; x++)
            {
                QPair<int, int> checkPos(x, sourceY);
                if (occupiedPositions.contains(checkPos) &&
                    occupiedPositions[checkPos] != sourceComponent)
                {
                    LOG_INFO_DEFAULT("无输出引脚元件虚拟路径冲突: 元件{}在({},{})的路径被元件{}阻塞",
                        sourceComponent, x, sourceY, occupiedPositions[checkPos]);
                    return true;
                }
            }
        }
    }
    return false;
}

// 获取OR块的所有输入分支元件
QList<QSharedPointer<LDComponent>> LDCoordinateFixer::getAllORBlockInputBranches(LDFile* ld, int orBlockNumber)
{
    QList<QSharedPointer<LDComponent>> inputBranches;

    // 检查OR块是否存在
    if (!ld->components->componentMap.contains(orBlockNumber))
    {
        LOG_ERROR_DEFAULT("OR块不存在: {}", orBlockNumber);
        return inputBranches;
    }

    QSharedPointer<LDComponent> orBlock = ld->components->componentMap.value(orBlockNumber);
    if (!orBlock || orBlock->Type != "Block" || orBlock->ChildType != "Func" || orBlock->AuxContent != "Or")
    {
        LOG_ERROR_DEFAULT("元件{}不是OR块", orBlockNumber);
        return inputBranches;
    }

    // 查找所有连接到OR块的输入分支（TargetConnectIndex > 0的连接）
    for (QSharedPointer<LDConnection>& connection : ld->connections->connectionList)
    {
        if (connection->TargetComponentNumber == orBlockNumber && connection->TargetConnectIndex > 0)
        {
            QSharedPointer<LDComponent> inputComponent = ld->components->componentMap.value(connection->SourceComponentNumber);
            if (inputComponent && inputComponent->Type != "Variable")
            {
                inputBranches.append(inputComponent);
                LOG_INFO_DEFAULT("找到OR块{}的输入分支: 元件{}, TargetConnectIndex: {}",
                    orBlockNumber, connection->SourceComponentNumber, connection->TargetConnectIndex);
            }
        }
    }

    // 按照TargetConnectIndex从小到大排序，确保遍历顺序的一致性
    std::sort(inputBranches.begin(), inputBranches.end(),
        [&](const QSharedPointer<LDComponent>& a, const QSharedPointer<LDComponent>& b)
        {
            // 找到对应的连接，比较TargetConnectIndex
            int connectIndexA = 0, connectIndexB = 0;
            for (QSharedPointer<LDConnection>& conn : ld->connections->connectionList)
            {
                if (conn->TargetComponentNumber == orBlockNumber && conn->SourceComponentNumber == a->Number)
                    connectIndexA = conn->TargetConnectIndex;
                if (conn->TargetComponentNumber == orBlockNumber && conn->SourceComponentNumber == b->Number)
                    connectIndexB = conn->TargetConnectIndex;
            }
            return connectIndexA < connectIndexB;
        });

    LOG_INFO_DEFAULT("OR块{}共有{}个输入分支", orBlockNumber, inputBranches.size());
    return inputBranches;
}



// 遍历从起始元件到目标元件的分支路径，按顺序分配TaskOrderNumber
void LDCoordinateFixer::traverseBranchPath(LDFile* ld, int startComponent, int endComponent, int networkNumber)
{
    LOG_INFO_DEFAULT("开始遍历分支路径: {} -> {}", startComponent, endComponent);

    int currentComponent = startComponent;

    while (currentComponent != -1 && currentComponent != endComponent)
    {
        // 检查元件是否已访问过
        if (visitedComponents.contains(currentComponent))
        {
            LOG_INFO_DEFAULT("元件{}已访问过，跳过", currentComponent);
            break;
        }

        // 获取当前元件
        QSharedPointer<LDComponent> component = ld->components->componentMap.value(currentComponent);
        if (!component)
        {
            LOG_ERROR_DEFAULT("元件{}不存在", currentComponent);
            break;
        }

        // 检查当前元件是否为OR块，如果是则需要先处理其输入分支（嵌套OR块处理）
        if (component->Type == "Block" && component->ChildType == "Func" && component->AuxContent == "Or")
        {
            LOG_INFO_DEFAULT("在分支路径中遇到嵌套OR块: {}, 先处理输入分支再分配TaskOrderNumber", currentComponent);

            // 获取OR块的所有输入分支
            QList<QSharedPointer<LDComponent>> inputBranches = getAllORBlockInputBranches(ld, currentComponent);

            // 遍历OR块的每个输入分支
            for (QSharedPointer<LDComponent> branchComponent : inputBranches)
            {
                if (branchComponent && !visitedComponents.contains(branchComponent->Number))
                {
                    LOG_INFO_DEFAULT("开始处理嵌套OR块{}的输入分支: {}", currentComponent, branchComponent->Number);

                    // 找到该分支的起始点
                    QSharedPointer<LDComponent> branchStartComponent = LDManager::instance().findFirstNodeInHorizontalPath(ld, branchComponent->Number);

                    if (branchStartComponent)
                    {
                        LOG_INFO_DEFAULT("找到嵌套OR块分支起始点: {} (分支终点: {})", branchStartComponent->Number, branchComponent->Number);

                        // 递归遍历从起始点到分支终点的完整路径
                        traverseBranchPath(ld, branchStartComponent->Number, branchComponent->Number, networkNumber);
                    }
                    else
                    {
                        LOG_ERROR_DEFAULT("无法找到嵌套OR块分支{}的起始点", branchComponent->Number);

                        // 如果找不到起始点，直接处理该分支元件
                        if (!visitedComponents.contains(branchComponent->Number))
                        {
                            branchComponent->TaskOrderNumber = taskOrderIndex;
                            taskOrderIndex++;
                            visitedComponents.insert(branchComponent->Number);
                            LOG_INFO_DEFAULT("嵌套OR块分支元件分配TaskOrderNumber - 编号: {}, TaskOrderNumber: {}",
                                branchComponent->Number, branchComponent->TaskOrderNumber);
                        }
                    }
                }
            }

            LOG_INFO_DEFAULT("嵌套OR块{}的输入分支处理完毕，现在分配OR块TaskOrderNumber", currentComponent);
        }

        // 分配TaskOrderNumber（对于普通元件或处理完输入分支的OR块）
        component->TaskOrderNumber = taskOrderIndex;
        taskOrderIndex++;
        visitedComponents.insert(currentComponent);

        LOG_INFO_DEFAULT("分支路径元件分配TaskOrderNumber - 编号: {}, TaskOrderNumber: {}",
            currentComponent, component->TaskOrderNumber);

        // 查找下一个水平串联元件
        QSharedPointer<LDComponent> nextComponent = LDManager::instance().findSerialSuccessor(ld, currentComponent);
        if (nextComponent)
        {
            currentComponent = nextComponent->Number;
        }
        else
        {
            break;
        }
    }

    // 处理目标元件（如果还没有被访问）
    if (currentComponent == endComponent && !visitedComponents.contains(endComponent))
    {
        QSharedPointer<LDComponent> endComp = ld->components->componentMap.value(endComponent);
        if (endComp)
        {
            endComp->TaskOrderNumber = taskOrderIndex;
            taskOrderIndex++;
            visitedComponents.insert(endComponent);

            LOG_INFO_DEFAULT("分支终点元件分配TaskOrderNumber - 编号: {}, TaskOrderNumber: {}",
                endComponent, endComp->TaskOrderNumber);
        }
    }
}

// 从START开始给每个元件的TaskOrderNumber赋值,按照从左到右水平串联遍历,如果遇到OR块时先从上到下遍历OR块的每个水平分支给每个元件的TaskOrderNumber赋值，如果碰到OR块嵌套继续先遍历OR块给TaskOrderNumber赋值
void LDCoordinateFixer::refreshTaskOrderNumber(LDFile* ld, int networkNumber, QSharedPointer<LDComponent> sourcecom)
{
    // 判断是否是变量元件
    if (sourcecom->Type == "Variable" && sourcecom->ChildType == "Local")
    {
        LOG_INFO_DEFAULT("变量元件{}跳过", sourcecom->Number);
        return;
    }

    // 检查是否为START元件的初始调用
    if (sourcecom->ParentNumber == 0 && sourcecom->InstanceName.contains("Start"))
    {
        tmpComStack.clear();
        visitedComponents.clear();
        taskOrderIndex = 0;
        sourcecom->TaskOrderNumber = taskOrderIndex;
        taskOrderIndex++;
        LOG_INFO_DEFAULT("START元件初始化 - 编号: {}, TaskOrderNumber: {}", sourcecom->Number, sourcecom->TaskOrderNumber);
    }

    // 验证网络存在性
    if (!ld->networks->networkMap.contains(networkNumber))
    {
        LOG_ERROR_DEFAULT("网络不存在，无法刷新任务顺序号 - 网络编号: {}", networkNumber);
        return;
    }

    // 防止重复遍历同一元件
    if (visitedComponents.contains(sourcecom->Number))
    {
        LOG_INFO_DEFAULT("元件{}已访问过，跳过", sourcecom->Number);
        return;
    }
    visitedComponents.insert(sourcecom->Number);

    // 查找水平串联的下一个元件（使用正确的ConnectIndex条件：SourceConnectIndex = 0, TargetConnectIndex = 0）
    QSharedPointer<LDComponent> nextComponent = LDManager::instance().findSerialSuccessor(ld, sourcecom->Number);

    if (nextComponent)
    {
        LOG_INFO_DEFAULT("找到水平串联后继元件 - 当前: {}, 后继: {}", sourcecom->Number, nextComponent->Number);

        // 检查后继元件是否为OR块
        if (nextComponent->Type == "Block" && nextComponent->ChildType == "Func" && nextComponent->AuxContent == "Or")
        {
            LOG_INFO_DEFAULT("遇到OR块: {}, 先处理输入分支再分配TaskOrderNumber", nextComponent->Number);

            // 获取OR块的所有输入分支
            QList<QSharedPointer<LDComponent>> inputBranches = getAllORBlockInputBranches(ld, nextComponent->Number);

            // 遍历OR块的每个输入分支
            for (QSharedPointer<LDComponent> branchComponent : inputBranches)
            {
                if (branchComponent && !visitedComponents.contains(branchComponent->Number))
                {
                    LOG_INFO_DEFAULT("开始处理OR块{}的输入分支: {}", nextComponent->Number, branchComponent->Number);

                    // 找到该分支的起始点
                    QSharedPointer<LDComponent> branchStartComponent = LDManager::instance().findFirstNodeInHorizontalPath(ld, branchComponent->Number);

                    if (branchStartComponent)
                    {
                        LOG_INFO_DEFAULT("找到分支起始点: {} (分支终点: {})", branchStartComponent->Number, branchComponent->Number);

                        // 遍历从起始点到分支终点的完整路径
                        traverseBranchPath(ld, branchStartComponent->Number, branchComponent->Number, networkNumber);
                    }
                    else
                    {
                        LOG_ERROR_DEFAULT("无法找到分支{}的起始点", branchComponent->Number);

                        // 如果找不到起始点，按原逻辑处理
                        if (!visitedComponents.contains(branchComponent->Number))
                        {
                            branchComponent->TaskOrderNumber = taskOrderIndex;
                            taskOrderIndex++;
                            LOG_INFO_DEFAULT("分支元件分配TaskOrderNumber - 编号: {}, TaskOrderNumber: {}",
                                branchComponent->Number, branchComponent->TaskOrderNumber);
                            refreshTaskOrderNumber(ld, networkNumber, branchComponent);
                        }
                    }
                }
            }

            // 处理完所有输入分支后，为OR块分配TaskOrderNumber
            if (!visitedComponents.contains(nextComponent->Number))
            {
                nextComponent->TaskOrderNumber = taskOrderIndex;
                taskOrderIndex++;
                visitedComponents.insert(nextComponent->Number);
                LOG_INFO_DEFAULT("OR块输入分支处理完毕，分配TaskOrderNumber - 编号: {}, TaskOrderNumber: {}",
                    nextComponent->Number, nextComponent->TaskOrderNumber);
            }

            // 继续遍历OR块之后的水平串联
            QSharedPointer<LDComponent> afterORComponent = LDManager::instance().findSerialSuccessor(ld, nextComponent->Number);
            if (afterORComponent && !visitedComponents.contains(afterORComponent->Number))
            {
                LOG_INFO_DEFAULT("OR块处理完毕，继续水平串联遍历: {}", afterORComponent->Number);
                afterORComponent->TaskOrderNumber = taskOrderIndex;
                taskOrderIndex++;
                LOG_INFO_DEFAULT("OR块后元件分配TaskOrderNumber - 编号: {}, TaskOrderNumber: {}",
                    afterORComponent->Number, afterORComponent->TaskOrderNumber);
                refreshTaskOrderNumber(ld, networkNumber, afterORComponent);
            }
        }
        else
        {
            // 普通元件，继续水平串联遍历
            if (!visitedComponents.contains(nextComponent->Number))
            {
                nextComponent->TaskOrderNumber = taskOrderIndex;
                taskOrderIndex++;
                LOG_INFO_DEFAULT("普通元件分配TaskOrderNumber - 编号: {}, TaskOrderNumber: {}",
                    nextComponent->Number, nextComponent->TaskOrderNumber);
                refreshTaskOrderNumber(ld, networkNumber, nextComponent);
            }
        }
    }
    else
    {
        LOG_INFO_DEFAULT("元件{}没有水平串联后继，遍历结束", sourcecom->Number);
    }
}


// 设置网络中每个元件的父元件 每个元件的父元件为与它TargetConnectIndex = 0相连的输入元件
void LDCoordinateFixer::setParentComponent(LDFile* ld, int networkNumber)
{
    LOG_INFO_DEFAULT("开始设置网络{}中每个元件的父元件", networkNumber);
    for (auto& component : ld->networks->networkMap.value(networkNumber)->componentMap)
    {
        // 如果是Start元件，则父元件为0
        if (component->InstanceName.startsWith("Start"))
        {
            component->ParentNumber = 0;
        }
        else
        {
            for (auto& connection : ld->connections->connectionList)
            {
                if (connection->TargetComponentNumber == component->Number && connection->TargetConnectIndex == 0)
                {
                    ld->components->componentMap[connection->TargetComponentNumber]->ParentNumber = connection->SourceComponentNumber;
                }
            }
        }
    }
    // 打印除挂载变量以外元件的父子关系
    // Type:Variable + ChildType:Local + AuxContent:1 = Variable元件
    for (auto& component : ld->components->componentMap)
    {
        if (component->Type != "Variable" && component->ChildType != "Local" && component->AuxContent != "???")
        {
            LOG_INFO_DEFAULT("父元件{} -> 元件{}", component->ParentNumber, component->Number);
        }
    }
}