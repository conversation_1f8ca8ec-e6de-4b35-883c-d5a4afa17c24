﻿import QtQuick 2.15
import QtQuick.Controls 2.15

//LD 设置0圈块
Item {
    id: control

    //主控件
    property var mainControl
    //配置
    property var config: LDEditorConfig
    //绑定的数据
    property var bindData

    width: img.width
    height: config.defaultConnectorHeight + img.height + config.defaultConnectorMargin

    //图片
    Image {
        id: img
        anchors {
            left: parent.left
            leftMargin: 0
            top: parent.top
            topMargin: config.defaultConnectorHeight + config.defaultConnectorMargin
        }
        width: config.defaultImageWidth
        height: config.defaultImageHeight
        source: "icons/ld_set0.png"
        fillMode: Image.Stretch
        //EN ENO引脚
        Repeater {
            model: bindData.Connectors
            Rectangle {
                id: enoline
                visible: model.Type === "EN" || model.Type === "ENO"
                width: config.defaultImageWidth / 2 - 14
                height: config.defaultLineWidth
                x: model.Type === "EN" ? 0 : config.defaultImageWidth / 2 + 14
                y: config.defaultLineHeightOffset
                color: mainControl.isOnline ? (model.Value ? config.defaultLineTrueColor : config.defaultLineFalseColor) : config.defaultLineColor
                //引脚的标签名称
                Text {
                    anchors {
                        horizontalCenter: parent.horizontalCenter
                        top: parent.top
                        topMargin: -config.fontPixelSize
                    }
                    visible: config.isShowLabelName
                    font.pixelSize: config.fontPixelSize
                    text: model.LabelName
                }
                //配置错误提示
                Text {
                    visible: model.Type === "ENO"
                             && (model.LabelName !== ""
                                 && model.LabelName !== "end")
                    x: 0
                    y: -config.fontPixelSize * 2
                    font.pixelSize: config.fontPixelSize * 4
                    text: "✕"
                    color: "red"
                }
            }
        }
    }
    //PARAM引脚
    Repeater {
        model: bindData.Connectors
        Rectangle {
            id: conn
            visible: model.Type === "PARAM"
            width: img.width * 3 / 2
            height: config.defaultConnectorHeight
            anchors {
                left: parent.left
                leftMargin: (img.width - conn.width) / 2
                top: parent.top
                topMargin: 0
            }
            border {
                width: 1
                color: config.defaultVaiableBorderColor
            }
            color: config.defaultVaiableBackgroundColor
            MouseArea {
                anchors.fill: parent
                hoverEnabled: true
                acceptedButtons: Qt.LeftButton | Qt.RightButton
                onClicked: {
                    mainControl.selectID = bindData.LevelInfo
                    if (mouse.button === Qt.RightButton) {
                        var positionInRoot = mapToItem(mainControl,
                                                       mouse.x, mouse.y)
                        mainControl.showMenu(positionInRoot.x,
                                             positionInRoot.y, model.Type,
                                             model.PinId)
                    }
                }
                onEntered: {
                    toolTip_DataType.visible = true
                }
                onExited: {
                    toolTip_DataType.visible = false
                }
            }
            ToolTip {
                id: toolTip_DataType
                parent: conn
                y: -35
                delay: 500
                text: model.DataType
                timeout: 3000
            }
            Text {
                id: t_vname
                anchors.centerIn: parent.Center
                font.pixelSize: config.fontPixelSize
                height: parent.height
                width: parent.width
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                text: model.LabelName
                color: config.defaultVaiableNameColor
            }
            //当前值显示
            Text {
                anchors {
                    left: t_vname.left
                    leftMargin: 0
                    bottom: t_vname.top
                    bottomMargin: 0
                }
                font.pixelSize: config.fontPixelSize
                height: t_vname.height
                width: t_vname.width
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                text: model.Value
                color: config.defaultVaiableValueColor
            }
        }
    }
}
