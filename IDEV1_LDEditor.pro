VERSION = 2.0.0

TEMPLATE = lib
TARGET = IDELDEditor
QT += qml quick xml
CONFIG += plugin qmltypes c++17
DEFINES += IDELDEDITOR_LIBRARY
DEFINES += APP_VERSION=\\\"$$VERSION\\\"

TARGET = $$qtLibraryTarget($$TARGET)
uri = IDELDEditor

QML_IMPORT_NAME = $$uri
QML_IMPORT_MAJOR_VERSION = 1

# Input
SOURCES += \
        # ld2poe.cpp \
        ccompatibletype.cpp \
        # ProBlockData.cpp \
        idev1_ldeditor_plugin.cpp \
        ldeditorversion.cpp \
        ldobject.cpp \
        ldmanager.cpp \
        extendfbc.cpp \
        ldcoordinatefixer.cpp

HEADERS += \
        # ld2poe.h \
        xmldata.h \
        # ProBlockData.h \
        CStructData.h \
        ccompatibletype.h \
        idev1_ldeditor_global.h \
        idev1_ldeditor_plugin.h \
        ldeditorversion.h \
        ldobject.h \
        ldmanager.h \
        extendfbc.h \
        ldcoordinatefixer.h

RESOURCES += \
    qml.qrc

DISTFILES = qmldir \

#中文
RC_LANG = 0x0004
# 公司名
QMAKE_TARGET_COMPANY = Three Gorges Intelligent Industrial Control Technology
# 产品名称
QMAKE_TARGET_PRODUCT = ReliAUTO Studio
# 详细描述
QMAKE_TARGET_DESCRIPTION = C++ Application Development Framework
# 版权
QMAKE_TARGET_COPYRIGHT = Copyright(C) 2023-2043 Three Gorges Intelligent Industrial Control Technology


!equals(_PRO_FILE_PWD_, $$OUT_PWD) {
    copy_qmldir.target = $$OUT_PWD/qmldir
    copy_qmldir.depends = $$_PRO_FILE_PWD_/qmldir
    copy_qmldir.commands = $(COPY_FILE) "$$replace(copy_qmldir.depends, /, $$QMAKE_DIR_SEP)" "$$replace(copy_qmldir.target, /, $$QMAKE_DIR_SEP)"
    QMAKE_EXTRA_TARGETS += copy_qmldir
    PRE_TARGETDEPS += $$copy_qmldir.target
}

qmldir.files = qmldir
unix {
    installPath = $$[QT_INSTALL_QML]/$$replace(uri, \., /)
    qmldir.path = $$installPath
    target.path = $$installPath
    INSTALLS += target qmldir
}


win32:CONFIG(release, debug | release)
{
    #指定要拷贝的文件目录为工程目录下release目录下的所有dll、lib文件，例如工程目录在D:\QT\Test
    #PWD就为D:/QT/Test，DllFile = D:/QT/Test/release/*.dll
    TargetDll = $$OUT_PWD/release/*.dll
    TargetLib = $$OUT_PWD/release/*.lib
    Targetplu = $$OUT_PWD/plugins.qmltypes
    Targetdir = $$OUT_PWD/qmldir
    Targethead = $$PWD/*.h
    #将输入目录中的"/"替换为"\"
    TargetDll = $$replace(TargetDll, /, \\)
    TargetLib = $$replace(TargetLib, /, \\)
    Targetplu = $$replace(Targetplu, /, \\)
    Targetdir = $$replace(Targetdir, /, \\)
    Targethead = $$replace(Targethead, /, \\)
    #将输出目录中的"/"替换为"\"
    OutputDllDir =  $$OUT_PWD/../IDEV1_MainApp/modules/$$uri/
    OutputDllDir = $$replace(OutputDllDir, /, \\)
    OutputRUNDllDir =  $$OUT_PWD/../build-IDEV1_MainApp-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/
    OutputRUNDllDir = $$replace(OutputRUNDllDir, /, \\)
    OutputLibDir =  $$OUT_PWD/../IDEV1_MainApp/lib/
    OutputLibDir = $$replace(OutputLibDir, /, \\)
    OutputHeadDir =  $$OUT_PWD/../IDEV1_MainApp/include/$$uri/
    OutputHeadDir = $$replace(OutputHeadDir, /, \\)
    //执行copy命令
    QMAKE_POST_LINK += copy /Y $$TargetDll $$OutputDllDir && copy /Y $$TargetDll $$OutputRUNDllDir && copy /Y $$TargetLib $$OutputLibDir && copy /Y $$Targetplu $$OutputDllDir && copy /Y $$Targetdir $$OutputDllDir && copy /Y $$Targethead $$OutputHeadDir
}

unix:!macx|win32: LIBS += -L$$PWD/IDECommon/include/spdlog/lib/ -lspdlog

INCLUDEPATH += $$PWD/IDECommon/include/spdlog/include
DEPENDPATH += $$PWD/IDECommon/include/spdlog/include

unix:!macx|win32: LIBS += -L$$PWD/IDECommon/lib/ -lIDECommon

INCLUDEPATH += $$PWD/IDECommon/include
DEPENDPATH += $$PWD/IDECommon/include

win32:!win32-g++: PRE_TARGETDEPS += $$PWD/IDECommon/lib/IDECommon.lib
else:unix:!macx|win32-g++: PRE_TARGETDEPS += $$PWD/IDECommon/lib/libIDECommon.a