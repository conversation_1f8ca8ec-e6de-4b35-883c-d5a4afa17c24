﻿#include "ccompatibletype.h"

CCompatibleType* CCompatibleType::m_pInstance = nullptr;

CCompatibleType* CCompatibleType::instance()
{
    if (m_pInstance == nullptr)
    {
        m_pInstance = new CCompatibleType();
    }

    return m_pInstance;
}

void CCompatibleType::readFile(const QString& filePath)
{
    QString fileName = filePath + "/CompatibleType.xml";
    //打开或创建文件
    QFile file(fileName);
    if (!file.open(QFile::ReadOnly))
    {
        return;
    }

    QDomDocument doc;
    if (!doc.setContent(&file))
    {
        file.close();
        return;
    }
    file.close();

    QDomElement root = doc.documentElement(); //返回根节点

    for (int i = 0; i < root.childNodes().size(); i++)
    {
        QDomNode node = root.childNodes().at(i);
        QString key = node.toElement().attribute("TypeName");

        QStringList value;
        for (int j = 0; j < node.toElement().childNodes().size(); j++)
        {
            QDomNode childNode = node.toElement().childNodes().at(j);
            value << childNode.toElement().attribute("TypeName");
        }

        m_data[key] = value;
    }
}

CCompatibleType::CCompatibleType()
{

}
